const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 获取商品列表（公开）
router.get('/', productController.getProducts);

// 获取单个商品（公开）
router.get('/:id', productController.getProductById);

// 创建商品（管理员）
router.post('/', authenticateToken, requireAdmin, productController.createProduct);

// 更新商品（管理员）
router.put('/:id', authenticateToken, requireAdmin, productController.updateProduct);

// 删除商品（管理员）
router.delete('/:id', authenticateToken, requireAdmin, productController.deleteProduct);

module.exports = router;
