import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import request from '@/utils/request'
import { getToken, setToken, removeToken, getUser, setUser, removeUser } from '@/utils/auth'

export interface User {
  id: number
  username: string
  email: string
  phone?: string
  role: 'admin' | 'user'
  status: 'active' | 'inactive'
  avatar?: string
  createdAt: string
  updatedAt: string
}

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  phone?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(getToken())
  const user = ref<User | null>(getUser())
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 登录
  const login = async (loginForm: LoginForm) => {
    loading.value = true
    try {
      const response = await request.post('/auth/login', loginForm)
      
      token.value = response.data.token
      user.value = response.data.user
      
      setToken(response.data.token)
      setUser(response.data.user)
      
      return response
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterForm) => {
    loading.value = true
    try {
      const response = await request.post('/auth/register', registerForm)
      
      token.value = response.data.token
      user.value = response.data.user
      
      setToken(response.data.token)
      setUser(response.data.user)
      
      return response
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    if (!token.value) return null
    
    try {
      const response = await request.get('/auth/me')
      user.value = response.data
      setUser(response.data)
      return response.data
    } catch (error) {
      // 如果获取用户信息失败，清除认证信息
      logout()
      throw error
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<User>) => {
    loading.value = true
    try {
      const response = await request.put('/auth/profile', profileData)
      user.value = response.data
      setUser(response.data)
      return response
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData: { currentPassword: string; newPassword: string }) => {
    loading.value = true
    try {
      const response = await request.put('/auth/password', passwordData)
      return response
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    user.value = null
    removeToken()
    removeUser()
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      try {
        await getCurrentUser()
      } catch (error) {
        console.error('初始化认证状态失败:', error)
      }
    }
  }

  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    
    // 方法
    login,
    register,
    getCurrentUser,
    updateProfile,
    changePassword,
    logout,
    initAuth
  }
})
