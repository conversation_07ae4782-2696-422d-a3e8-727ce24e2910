import request from '@/utils/request'
import type { ApiResponse, Category, QueryParams } from './types'

export interface CategoryForm {
  name: string
  description?: string
  sort_order?: number
}

// 获取分类列表
export function getCategories(params?: QueryParams): Promise<ApiResponse<Category[]>> {
  return request.get('/categories', { params })
}

// 获取单个分类
export function getCategoryById(id: number, params?: QueryParams): Promise<ApiResponse<Category>> {
  return request.get(`/categories/${id}`, { params })
}

// 创建分类
export function createCategory(data: CategoryForm): Promise<ApiResponse<Category>> {
  return request.post('/categories', data)
}

// 更新分类
export function updateCategory(id: number, data: Partial<CategoryForm>): Promise<ApiResponse<Category>> {
  return request.put(`/categories/${id}`, data)
}

// 删除分类
export function deleteCategory(id: number): Promise<ApiResponse<null>> {
  return request.delete(`/categories/${id}`)
}
