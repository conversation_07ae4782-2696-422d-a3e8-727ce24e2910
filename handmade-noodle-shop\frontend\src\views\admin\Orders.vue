<template>
  <div class="orders-admin">
    <div class="page-header">
      <h1>订单管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showStatsDialog">
          <el-icon><DataAnalysis /></el-icon>
          订单统计
        </el-button>
        <el-button type="success" @click="exportOrders">
          <el-icon><Download /></el-icon>
          导出订单
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ orderStats.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon processing">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ orderStats.processing }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ orderStats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon revenue">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ orderStats.revenue }}</div>
              <div class="stat-label">今日营收</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入订单号"
            clearable
            @keyup.enter="loadOrders"
          />
        </el-form-item>

        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="待确认" value="pending" />
            <el-option label="已确认" value="confirmed" />
            <el-option label="制作中" value="preparing" />
            <el-option label="待取货" value="ready" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="下单时间">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadOrders">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card>
      <el-table
        :data="orders"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="order_no" label="订单号" width="150" />
        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div>{{ row.user?.username }}</div>
            <div class="user-phone">{{ row.user?.phone }}</div>
          </template>
        </el-table-column>
        <el-table-column label="商品信息" min-width="200">
          <template #default="{ row }">
            <div v-for="item in row.items" :key="item.id" class="order-item">
              {{ item.product?.name }} × {{ item.quantity_jin }}斤
            </div>
          </template>
        </el-table-column>
        <el-table-column label="金额" width="120">
          <template #default="{ row }">
            <div class="amount-info">
              <div v-if="row.discount_amount > 0" class="original-amount">
                原价：¥{{ row.total_amount }}
              </div>
              <div v-if="row.discount_amount > 0" class="discount-amount">
                优惠：-¥{{ row.discount_amount }}
              </div>
              <div class="final-amount">
                实付：¥{{ row.final_amount }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusType(row.status)">
              {{ formatOrderStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="取货时间" width="180">
          <template #default="{ row }">
            {{ row.pickup_time ? formatDateTime(row.pickup_time) : '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="下单时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewOrderDetail(row)">
              详情
            </el-button>
            <el-button
              type="text"
              @click="updateOrderStatus(row)"
              v-if="row.status !== 'completed' && row.status !== 'cancelled'"
            >
              更新状态
            </el-button>
            <el-button
              type="text"
              danger
              @click="cancelOrder(row)"
              v-if="row.status === 'pending'"
            >
              取消订单
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadOrders"
          @current-change="loadOrders"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog
      title="订单详情"
      v-model="detailDialogVisible"
      width="800px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">
            {{ selectedOrder.order_no }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(selectedOrder.status)">
              {{ formatOrderStatus(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户姓名">
            {{ selectedOrder.user?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ selectedOrder.user?.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="下单时间">
            {{ formatDateTime(selectedOrder.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="取货时间">
            {{ selectedOrder.pickup_time ? formatDateTime(selectedOrder.pickup_time) : '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="订单备注" :span="2">
            {{ selectedOrder.notes || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <h3 style="margin: 20px 0 10px 0;">商品明细</h3>
        <el-table :data="selectedOrder.items" border>
          <el-table-column prop="product.name" label="商品名称" />
          <el-table-column prop="product.sku" label="SKU" width="120" />
          <el-table-column label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.unit_price }}/斤
            </template>
          </el-table-column>
          <el-table-column label="数量" width="100">
            <template #default="{ row }">
              {{ row.quantity_jin }}斤
            </template>
          </el-table-column>
          <el-table-column label="小计" width="100">
            <template #default="{ row }">
              ¥{{ row.subtotal }}
            </template>
          </el-table-column>
        </el-table>

        <div class="order-summary">
          <el-row>
            <el-col :span="12" :offset="12">
              <div class="summary-item">
                <span>商品总额：</span>
                <span>¥{{ selectedOrder.total_amount }}</span>
              </div>
              <div class="summary-item" v-if="selectedOrder.discount_amount > 0">
                <span>优惠金额：</span>
                <span class="discount">-¥{{ selectedOrder.discount_amount }}</span>
              </div>
              <div class="summary-item total">
                <span>实付金额：</span>
                <span>¥{{ selectedOrder.final_amount }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>

    <!-- 更新状态对话框 -->
    <el-dialog
      title="更新订单状态"
      v-model="statusDialogVisible"
      width="400px"
    >
      <el-form :model="statusForm" label-width="80px">
        <el-form-item label="当前状态">
          <el-tag :type="getOrderStatusType(statusForm.current_status)">
            {{ formatOrderStatus(statusForm.current_status) }}
          </el-tag>
        </el-form-item>

        <el-form-item label="新状态">
          <el-select v-model="statusForm.new_status" placeholder="请选择新状态">
            <el-option
              v-for="status in getAvailableStatuses(statusForm.current_status)"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="statusForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入状态更新备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="statusDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmStatusUpdate" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DataAnalysis,
  Download,
  Clock,
  Loading,
  Check,
  Money,
  Search
} from '@element-plus/icons-vue'
import { formatOrderStatus, formatDateTime } from '@/utils/format'
import type { Order } from '@/api/types'

// 订单数据
const orders = ref<Order[]>([])
const selectedOrder = ref<Order | null>(null)
const loading = ref(false)
const submitting = ref(false)
const detailDialogVisible = ref(false)
const statusDialogVisible = ref(false)

// 统计数据
const orderStats = ref({
  pending: 0,
  processing: 0,
  completed: 0,
  revenue: 0
})

// 搜索表单
const searchForm = ref({
  search: '',
  status: '',
  date_range: [] as string[]
})

// 分页
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

// 状态更新表单
const statusForm = ref({
  order_id: 0,
  current_status: '',
  new_status: '',
  notes: ''
})

// 获取订单状态类型
const getOrderStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    confirmed: 'info',
    preparing: 'primary',
    ready: 'success',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取可用的状态转换
const getAvailableStatuses = (currentStatus: string) => {
  const statusFlow: Record<string, Array<{ label: string; value: string }>> = {
    pending: [
      { label: '确认订单', value: 'confirmed' },
      { label: '取消订单', value: 'cancelled' }
    ],
    confirmed: [
      { label: '开始制作', value: 'preparing' }
    ],
    preparing: [
      { label: '制作完成', value: 'ready' }
    ],
    ready: [
      { label: '完成订单', value: 'completed' }
    ]
  }
  return statusFlow[currentStatus] || []
}

// 加载订单列表
const loadOrders = async () => {
  loading.value = true
  try {
    // 模拟数据，实际应该调用API
    const mockData = [
      {
        id: 1,
        order_no: '240101001',
        user_id: 1,
        total_amount: 50.00,
        discount_amount: 5.00,
        final_amount: 45.00,
        status: 'pending',
        pickup_time: null,
        notes: '请尽快制作',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        user: {
          id: 1,
          username: '张三',
          email: '<EMAIL>',
          phone: '13800138000'
        },
        items: [
          {
            id: 1,
            order_id: 1,
            product_id: 1,
            quantity_jin: 2.5,
            unit_price: 12.00,
            subtotal: 30.00,
            product: {
              id: 1,
              sku: 'g001',
              name: '手工拉面',
              images: []
            }
          },
          {
            id: 2,
            order_id: 1,
            product_id: 2,
            quantity_jin: 2.0,
            unit_price: 10.00,
            subtotal: 20.00,
            product: {
              id: 2,
              sku: 'g003',
              name: '手工面片',
              images: []
            }
          }
        ]
      }
    ]

    orders.value = mockData
    pagination.value.total = mockData.length

    // 更新统计数据
    updateOrderStats()
  } catch (error) {
    console.error('加载订单列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateOrderStats = () => {
  const stats = {
    pending: 0,
    processing: 0,
    completed: 0,
    revenue: 0
  }

  orders.value.forEach(order => {
    if (order.status === 'pending') {
      stats.pending++
    } else if (['confirmed', 'preparing', 'ready'].includes(order.status)) {
      stats.processing++
    } else if (order.status === 'completed') {
      stats.completed++
      // 计算今日营收（这里简化处理）
      stats.revenue += order.final_amount
    }
  })

  orderStats.value = stats
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    search: '',
    status: '',
    date_range: []
  }
  pagination.value.page = 1
  loadOrders()
}

// 查看订单详情
const viewOrderDetail = (order: Order) => {
  selectedOrder.value = order
  detailDialogVisible.value = true
}

// 更新订单状态
const updateOrderStatus = (order: Order) => {
  statusForm.value = {
    order_id: order.id,
    current_status: order.status,
    new_status: '',
    notes: ''
  }
  statusDialogVisible.value = true
}

// 确认状态更新
const confirmStatusUpdate = async () => {
  if (!statusForm.value.new_status) {
    ElMessage.warning('请选择新状态')
    return
  }

  try {
    submitting.value = true

    // 这里应该调用API更新状态
    ElMessage.success('订单状态更新成功')
    statusDialogVisible.value = false
    loadOrders()
  } catch (error) {
    console.error('更新订单状态失败:', error)
  } finally {
    submitting.value = false
  }
}

// 取消订单
const cancelOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单"${order.order_no}"吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该调用API取消订单
    ElMessage.success('订单取消成功')
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
    }
  }
}

// 显示统计对话框
const showStatsDialog = () => {
  ElMessage.info('订单统计功能开发中...')
}

// 导出订单
const exportOrders = () => {
  ElMessage.info('订单导出功能开发中...')
}

onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.orders-admin {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
}

.stat-icon.processing {
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #66bb6a 0%, #43a047 100%);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #ab47bc 0%, #8e24aa 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.search-card {
  margin-bottom: 20px;
}

.user-phone {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.order-item {
  margin-bottom: 4px;
  font-size: 14px;
}

.amount-info {
  text-align: right;
}

.original-amount {
  font-size: 12px;
  color: #909399;
  text-decoration: line-through;
}

.discount-amount {
  font-size: 12px;
  color: #f56c6c;
}

.final-amount {
  font-weight: 600;
  color: #303133;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.order-detail {
  padding: 0;
}

.order-summary {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-item.total {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-top: 1px solid #e4e7ed;
  padding-top: 8px;
  margin-top: 8px;
}

.discount {
  color: #f56c6c;
}
</style></script>
