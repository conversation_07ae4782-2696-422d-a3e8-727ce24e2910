const { Order, OrderItem, Product, User, Promotion, Inventory, sequelize } = require('../models');
const { Op } = require('sequelize');
const response = require('../utils/response');

// 生成订单号
const generateOrderNo = () => {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hour = now.getHours().toString().padStart(2, '0');
  const minute = now.getMinutes().toString().padStart(2, '0');
  const second = now.getSeconds().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `${year}${month}${day}${hour}${minute}${second}${random}`;
};

// 获取订单列表
const getOrders = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status,
      user_id,
      start_date,
      end_date,
      search
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    const whereClause = {};
    
    if (status) {
      whereClause.status = status;
    }
    
    // 如果不是管理员，只能查看自己的订单
    if (req.user.role !== 'admin') {
      whereClause.user_id = req.user.id;
    } else if (user_id) {
      whereClause.user_id = user_id;
    }
    
    if (start_date && end_date) {
      whereClause.createdAt = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    }
    
    if (search) {
      whereClause.order_no = { [Op.like]: `%${search}%` };
    }

    // 查询订单
    const { count, rows: orders } = await Order.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email', 'phone']
        },
        {
          model: Promotion,
          as: 'promotion',
          attributes: ['id', 'title', 'type', 'discount_value'],
          required: false
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'sku', 'name', 'images']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    response.paginated(res, orders, {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count
    }, '获取订单列表成功');

  } catch (error) {
    console.error('获取订单列表错误:', error);
    response.serverError(res, '获取订单列表失败');
  }
};

// 获取单个订单
const getOrderById = async (req, res) => {
  try {
    const { id } = req.params;

    const whereClause = { id };
    
    // 如果不是管理员，只能查看自己的订单
    if (req.user.role !== 'admin') {
      whereClause.user_id = req.user.id;
    }

    const order = await Order.findOne({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email', 'phone']
        },
        {
          model: Promotion,
          as: 'promotion',
          attributes: ['id', 'title', 'type', 'discount_value', 'gift_description'],
          required: false
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'sku', 'name', 'images', 'category_id']
            }
          ]
        }
      ]
    });

    if (!order) {
      return response.notFound(res, '订单不存在');
    }

    response.success(res, order, '获取订单成功');
  } catch (error) {
    console.error('获取订单错误:', error);
    response.serverError(res, '获取订单失败');
  }
};

// 创建订单
const createOrder = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { items, promotion_id, pickup_time, notes } = req.body;
    const user_id = req.user.id;

    // 数据验证
    if (!items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return response.validationError(res, ['订单项不能为空']);
    }

    // 验证商品和库存
    let total_amount = 0;
    const orderItems = [];

    for (const item of items) {
      const { product_id, quantity_jin } = item;
      
      if (!product_id || !quantity_jin || quantity_jin <= 0) {
        await transaction.rollback();
        return response.validationError(res, ['商品ID和数量必须有效']);
      }

      // 获取商品信息
      const product = await Product.findByPk(product_id, {
        include: [
          {
            model: Inventory,
            as: 'inventory'
          }
        ]
      });

      if (!product || product.status !== 'active') {
        await transaction.rollback();
        return response.error(res, `商品 ${product_id} 不存在或已下架`, 400);
      }

      // 检查库存
      if (product.inventory.stock_jin < quantity_jin) {
        await transaction.rollback();
        return response.error(res, `商品 ${product.name} 库存不足`, 400);
      }

      const subtotal = parseFloat(quantity_jin) * parseFloat(product.price_per_jin);
      total_amount += subtotal;

      orderItems.push({
        product_id,
        product,
        quantity_jin: parseFloat(quantity_jin),
        unit_price: parseFloat(product.price_per_jin),
        subtotal
      });
    }

    // 检查促销活动
    let promotion = null;
    let discount_amount = 0;

    if (promotion_id) {
      promotion = await Promotion.findByPk(promotion_id);
      
      if (promotion && promotion.status === 'active') {
        const now = new Date();
        if (now >= promotion.start_time && now <= promotion.end_time) {
          // 计算折扣
          if (promotion.type === 'full_reduction' && total_amount >= promotion.condition_amount) {
            discount_amount = promotion.discount_value;
          } else if (promotion.type === 'discount') {
            discount_amount = total_amount * (promotion.discount_value / 100);
          }
        }
      }
    }

    const final_amount = total_amount - discount_amount;

    // 生成订单号
    const order_no = generateOrderNo();

    // 创建订单
    const order = await Order.create({
      order_no,
      user_id,
      total_amount,
      discount_amount,
      final_amount,
      promotion_id: promotion ? promotion.id : null,
      pickup_time: pickup_time ? new Date(pickup_time) : null,
      notes: notes?.trim(),
      status: 'pending'
    }, { transaction });

    // 创建订单项并更新库存
    for (const item of orderItems) {
      await OrderItem.create({
        order_id: order.id,
        product_id: item.product_id,
        quantity_jin: item.quantity_jin,
        unit_price: item.unit_price,
        subtotal: item.subtotal
      }, { transaction });

      // 更新库存
      await Inventory.update(
        { 
          stock_jin: item.product.inventory.stock_jin - item.quantity_jin,
          last_updated: new Date()
        },
        { 
          where: { product_id: item.product_id },
          transaction 
        }
      );
    }

    await transaction.commit();

    // 获取完整的订单信息
    const fullOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email', 'phone']
        },
        {
          model: Promotion,
          as: 'promotion',
          attributes: ['id', 'title', 'type', 'discount_value'],
          required: false
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'sku', 'name', 'images']
            }
          ]
        }
      ]
    });

    response.success(res, fullOrder, '创建订单成功', 201);

  } catch (error) {
    await transaction.rollback();
    console.error('创建订单错误:', error);
    response.serverError(res, '创建订单失败');
  }
};

// 更新订单状态（管理员或订单所有者）
const updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    // 数据验证
    const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'completed', 'cancelled'];
    if (!status || !validStatuses.includes(status)) {
      return response.validationError(res, ['订单状态无效']);
    }

    const whereClause = { id };
    
    // 如果不是管理员，只能更新自己的订单，且只能取消
    if (req.user.role !== 'admin') {
      whereClause.user_id = req.user.id;
      if (status !== 'cancelled') {
        return response.forbidden(res, '只能取消自己的订单');
      }
    }

    const order = await Order.findOne({ where: whereClause });
    if (!order) {
      return response.notFound(res, '订单不存在');
    }

    // 检查状态转换是否合理
    if (order.status === 'completed' || order.status === 'cancelled') {
      return response.error(res, '订单已完成或已取消，无法修改状态', 400);
    }

    // 如果取消订单，需要恢复库存
    if (status === 'cancelled' && order.status !== 'cancelled') {
      const orderItems = await OrderItem.findAll({
        where: { order_id: id }
      });

      for (const item of orderItems) {
        await Inventory.increment(
          'stock_jin',
          { 
            by: item.quantity_jin,
            where: { product_id: item.product_id }
          }
        );
      }
    }

    // 更新订单
    const updateData = { status };
    if (notes !== undefined) {
      updateData.notes = notes?.trim();
    }

    await order.update(updateData);

    response.success(res, order, '更新订单状态成功');

  } catch (error) {
    console.error('更新订单状态错误:', error);
    response.serverError(res, '更新订单状态失败');
  }
};

// 获取订单统计（管理员）
const getOrderStats = async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    const whereClause = {};
    if (start_date && end_date) {
      whereClause.createdAt = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    }

    // 订单状态统计
    const statusStats = await Order.findAll({
      where: whereClause,
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('final_amount')), 'total_amount']
      ],
      group: ['status']
    });

    // 总体统计
    const totalStats = await Order.findOne({
      where: whereClause,
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'total_orders'],
        [sequelize.fn('SUM', sequelize.col('final_amount')), 'total_revenue'],
        [sequelize.fn('AVG', sequelize.col('final_amount')), 'avg_order_value']
      ]
    });

    response.success(res, {
      status_stats: statusStats,
      total_stats: totalStats
    }, '获取订单统计成功');

  } catch (error) {
    console.error('获取订单统计错误:', error);
    response.serverError(res, '获取订单统计失败');
  }
};

module.exports = {
  getOrders,
  getOrderById,
  createOrder,
  updateOrderStatus,
  getOrderStats
};
