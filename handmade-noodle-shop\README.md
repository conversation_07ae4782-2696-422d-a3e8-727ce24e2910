# 手工面食店电商网站系统

一个完整的手工面食店电商网站系统，包含前台展示和后台管理功能。

## 项目结构

```
handmade-noodle-shop/
├── frontend/          # Vue 3 前端项目
├── backend/           # Node.js 后端项目
└── README.md         # 项目说明
```

## 技术栈

### 前端
- Vue 3 + TypeScript
- Vite 构建工具
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router 路由管理
- Axios HTTP客户端

### 后端
- Node.js + Express
- Sequelize ORM
- MySQL 8 数据库
- JWT 身份认证
- Multer 文件上传
- Helmet 安全中间件

## 功能特性

### 前台用户界面
- 首页展示促销活动横幅
- 商品分类浏览（面条、面片、烧饼等）
- 商品详情页面，支持多图展示
- 右上角显示用户IP所在城市和当前天气
- 用户注册登录功能
- 在线预订/下单功能（到店自提）
- 顾客评价系统

### 后台管理系统
- 管理员登录验证
- 商品管理（增删改查，支持图片上传）
- 库存管理（按斤计算）
- 促销活动管理（时间段控制）
- 订单管理
- 用户管理
- 数据统计看板

## 数据库设计

- 用户表（支持普通用户和管理员角色）
- 商品表（SKU以'g'开头，价格按斤计算）
- 商品分类表
- 库存表
- 促销活动表
- 订单表
- 订单详情表
- 系统配置表

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8+
- npm 或 yarn

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd handmade-noodle-shop
```

2. 安装后端依赖
```bash
cd backend
npm install
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接信息
```

4. 创建数据库
```bash
node scripts/createDatabase.js
```

5. 启动后端服务
```bash
npm run dev
```

6. 安装前端依赖
```bash
cd ../frontend
npm install
```

7. 启动前端服务
```bash
npm run dev
```

### 访问地址
- 前端：http://localhost:5173
- 后端API：http://localhost:3000

## 开发进度

### ✅ 已完成
- [x] 项目初始化
- [x] 数据库设计和模型创建
- [x] 基础服务器配置
- [x] 前端项目搭建
- [x] Element Plus 集成

### 🚧 进行中
- [ ] 后端API开发
- [ ] 前端页面开发
- [ ] 功能联调

### 📋 待开发
- [ ] 用户认证系统
- [ ] 商品管理功能
- [ ] 订单管理功能
- [ ] 天气API集成
- [ ] 部署配置

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
