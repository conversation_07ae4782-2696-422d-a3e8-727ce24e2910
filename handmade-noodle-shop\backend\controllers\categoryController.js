const { Category, Product } = require('../models');
const response = require('../utils/response');
const { Op } = require('sequelize');

// 获取所有分类
const getCategories = async (req, res) => {
  try {
    const { status = 'active', includeProducts = false } = req.query;

    const whereClause = {};
    if (status !== 'all') {
      whereClause.status = status;
    }

    const includeOptions = [];
    if (includeProducts === 'true') {
      includeOptions.push({
        model: Product,
        as: 'products',
        where: { status: 'active' },
        required: false,
        attributes: ['id', 'name', 'sku', 'price_per_jin', 'images']
      });
    }

    const categories = await Category.findAll({
      where: whereClause,
      include: includeOptions,
      order: [['sort_order', 'ASC'], ['createdAt', 'DESC']]
    });

    response.success(res, categories, '获取分类成功');
  } catch (error) {
    console.error('获取分类错误:', error);
    response.serverError(res, '获取分类失败');
  }
};

// 获取单个分类
const getCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    const { includeProducts = false } = req.query;

    const includeOptions = [];
    if (includeProducts === 'true') {
      includeOptions.push({
        model: Product,
        as: 'products',
        where: { status: 'active' },
        required: false,
        attributes: ['id', 'name', 'sku', 'price_per_jin', 'images']
      });
    }

    const category = await Category.findByPk(id, {
      include: includeOptions
    });

    if (!category) {
      return response.notFound(res, '分类不存在');
    }

    response.success(res, category, '获取分类成功');
  } catch (error) {
    console.error('获取分类错误:', error);
    response.serverError(res, '获取分类失败');
  }
};

// 创建分类（管理员）
const createCategory = async (req, res) => {
  try {
    const { name, description, sort_order = 0 } = req.body;

    // 数据验证
    if (!name || name.trim().length === 0) {
      return response.validationError(res, ['分类名称不能为空']);
    }

    // 检查分类名是否已存在
    const existingCategory = await Category.findOne({
      where: { name: name.trim() }
    });

    if (existingCategory) {
      return response.error(res, '分类名称已存在', 409);
    }

    // 创建分类
    const category = await Category.create({
      name: name.trim(),
      description: description?.trim(),
      sort_order: parseInt(sort_order) || 0
    });

    response.success(res, category, '创建分类成功', 201);
  } catch (error) {
    console.error('创建分类错误:', error);
    response.serverError(res, '创建分类失败');
  }
};

// 更新分类（管理员）
const updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, sort_order, status } = req.body;

    const category = await Category.findByPk(id);
    if (!category) {
      return response.notFound(res, '分类不存在');
    }

    // 数据验证
    const errors = [];
    if (name && name.trim().length === 0) {
      errors.push('分类名称不能为空');
    }

    if (status && !['active', 'inactive'].includes(status)) {
      errors.push('状态值无效');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    // 检查分类名是否已被其他分类使用
    if (name && name.trim() !== category.name) {
      const existingCategory = await Category.findOne({
        where: { 
          name: name.trim(),
          id: { [Op.ne]: id }
        }
      });

      if (existingCategory) {
        return response.error(res, '分类名称已存在', 409);
      }
    }

    // 更新分类
    const updateData = {};
    if (name) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim();
    if (sort_order !== undefined) updateData.sort_order = parseInt(sort_order) || 0;
    if (status) updateData.status = status;

    await category.update(updateData);

    response.success(res, category, '更新分类成功');
  } catch (error) {
    console.error('更新分类错误:', error);
    response.serverError(res, '更新分类失败');
  }
};

// 删除分类（管理员）
const deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;

    const category = await Category.findByPk(id);
    if (!category) {
      return response.notFound(res, '分类不存在');
    }

    // 检查是否有商品使用此分类
    const productCount = await Product.count({
      where: { category_id: id }
    });

    if (productCount > 0) {
      return response.error(res, '该分类下还有商品，无法删除', 409);
    }

    await category.destroy();

    response.success(res, null, '删除分类成功');
  } catch (error) {
    console.error('删除分类错误:', error);
    response.serverError(res, '删除分类失败');
  }
};

module.exports = {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory
};
