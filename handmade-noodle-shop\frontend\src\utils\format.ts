// 格式化工具函数

// 格式化价格
export function formatPrice(price: number): string {
  return `¥${price.toFixed(2)}`
}

// 格式化重量（斤）
export function formatWeight(weight: number): string {
  return `${weight}斤`
}

// 格式化日期时间
export function formatDateTime(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化日期
export function formatDate(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 格式化时间
export function formatTime(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化订单状态
export function formatOrderStatus(status: string): string {
  const statusMap: Record<string, string> = {
    pending: '待确认',
    confirmed: '已确认',
    preparing: '制作中',
    ready: '待取货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 格式化促销类型
export function formatPromotionType(type: string): string {
  const typeMap: Record<string, string> = {
    discount: '折扣',
    gift: '赠品',
    full_reduction: '满减'
  }
  return typeMap[type] || type
}

// 格式化用户角色
export function formatUserRole(role: string): string {
  const roleMap: Record<string, string> = {
    admin: '管理员',
    user: '普通用户'
  }
  return roleMap[role] || role
}

// 格式化状态
export function formatStatus(status: string): string {
  const statusMap: Record<string, string> = {
    active: '启用',
    inactive: '禁用',
    expired: '已过期'
  }
  return statusMap[status] || status
}

// 截取文本
export function truncateText(text: string, maxLength: number = 50): string {
  if (text.length <= maxLength) {
    return text
  }
  return text.substring(0, maxLength) + '...'
}

// 生成随机颜色
export function getRandomColor(): string {
  const colors = [
    '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', 
    '#909399', '#C0C4CC', '#606266', '#303133'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}
