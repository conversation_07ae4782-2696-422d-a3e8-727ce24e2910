<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="sidebar">
        <div class="logo">
          <h2 v-if="!appStore.sidebarCollapsed">🍜 面食店管理</h2>
          <h2 v-else>🍜</h2>
        </div>
        
        <el-menu
          :default-active="$route.path"
          :collapse="appStore.sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/admin/dashboard">
            <el-icon><i class="el-icon-odometer"></i></el-icon>
            <span>仪表盘</span>
          </el-menu-item>

          <el-menu-item index="/admin/categories">
            <el-icon><i class="el-icon-menu"></i></el-icon>
            <span>分类管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/products">
            <el-icon><i class="el-icon-goods"></i></el-icon>
            <span>商品管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/inventory">
            <el-icon><i class="el-icon-box"></i></el-icon>
            <span>库存管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/promotions">
            <el-icon><i class="el-icon-present"></i></el-icon>
            <span>促销管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/orders">
            <el-icon><i class="el-icon-document"></i></el-icon>
            <span>订单管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/users">
            <el-icon><i class="el-icon-user"></i></el-icon>
            <span>用户管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/settings">
            <el-icon><i class="el-icon-setting"></i></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button 
              type="text" 
              @click="appStore.toggleSidebar"
              class="sidebar-toggle"
            >
              <el-icon><i :class="appStore.sidebarCollapsed ? 'el-icon-expand' : 'el-icon-fold'"></i></el-icon>
            </el-button>
            
            <!-- 面包屑导航 -->
            <el-breadcrumb separator="/">
              <el-breadcrumb-item 
                v-for="item in appStore.breadcrumbs" 
                :key="item.title"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- 天气信息 -->
            <div class="weather-info" v-if="weather">
              <el-icon><i class="el-icon-sunny"></i></el-icon>
              <span>{{ weather.city }} {{ weather.temperature }}°C {{ weather.description }}</span>
            </div>
            
            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserCommand">
              <div class="user-info">
                <el-avatar :src="authStore.user?.avatar" :size="32">
                  {{ authStore.user?.username?.charAt(0) }}
                </el-avatar>
                <span class="username">{{ authStore.user?.username }}</span>
                <el-icon><i class="el-icon-arrow-down"></i></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                  <el-dropdown-item command="shop">前台页面</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { ElMessage } from 'element-plus'
// 使用字符串图标名称，避免导入问题

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 天气信息
const weather = ref<any>(null)

// 侧边栏宽度
const sidebarWidth = computed(() => {
  return appStore.sidebarCollapsed ? '64px' : '200px'
})

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 打开个人设置对话框
      break
    case 'shop':
      router.push('/shop')
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      ElMessage.success('已退出登录')
      break
  }
}

// 获取天气信息
const getWeather = async () => {
  try {
    // 这里可以调用天气API
    // const response = await request.get('/weather')
    // weather.value = response.data
  } catch (error) {
    console.error('获取天气信息失败:', error)
  }
}

onMounted(() => {
  getWeather()
})
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: white;
  border-bottom: 1px solid #434a50;
}

.logo img {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.logo h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.sidebar-menu {
  border: none;
  background-color: #304156;
}

.sidebar-menu .el-menu-item {
  color: #bfcbd9;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-menu-item.is-active {
  background-color: #263445;
  color: #409eff;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  margin-right: 20px;
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
}
</style>
