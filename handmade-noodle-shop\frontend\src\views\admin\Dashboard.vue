<template>
  <div class="dashboard">
    <div class="page-header">
      <h1>仪表盘</h1>
      <p>欢迎回来，{{ authStore.user?.username }}！</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon orders">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon revenue">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ stats.totalRevenue }}</div>
              <div class="stat-label">总营业额</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon products">
              <el-icon><Goods /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalProducts }}</div>
              <div class="stat-label">商品总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalUsers }}</div>
              <div class="stat-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表和列表 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近订单</span>
              <el-button type="text" @click="$router.push('/admin/orders')">
                查看全部
              </el-button>
            </div>
          </template>
          
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="order_no" label="订单号" width="120" />
            <el-table-column prop="user.username" label="用户" width="100" />
            <el-table-column prop="final_amount" label="金额" width="80">
              <template #default="{ row }">
                ¥{{ row.final_amount }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="getOrderStatusType(row.status)">
                  {{ formatOrderStatus(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>低库存商品</span>
              <el-button type="text" @click="$router.push('/admin/inventory')">
                查看全部
              </el-button>
            </div>
          </template>
          
          <el-table :data="lowStockProducts" style="width: 100%">
            <el-table-column prop="product.name" label="商品名称" />
            <el-table-column prop="stock_jin" label="当前库存" width="100">
              <template #default="{ row }">
                <span class="low-stock">{{ row.stock_jin }}斤</span>
              </template>
            </el-table-column>
            <el-table-column prop="min_stock_jin" label="最低库存" width="100">
              <template #default="{ row }">
                {{ row.min_stock_jin }}斤
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快捷操作 -->
    <el-card class="quick-actions">
      <template #header>
        <span>快捷操作</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button 
            type="primary" 
            @click="$router.push('/admin/products')"
            class="quick-action-btn"
          >
            <el-icon><Plus /></el-icon>
            添加商品
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button 
            type="success" 
            @click="$router.push('/admin/promotions')"
            class="quick-action-btn"
          >
            <el-icon><Present /></el-icon>
            创建促销
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button 
            type="warning" 
            @click="$router.push('/admin/inventory')"
            class="quick-action-btn"
          >
            <el-icon><Box /></el-icon>
            库存管理
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button 
            type="info" 
            @click="$router.push('/admin/orders')"
            class="quick-action-btn"
          >
            <el-icon><Document /></el-icon>
            订单管理
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { formatOrderStatus } from '@/utils/format'
import {
  Document,
  Money,
  Goods,
  User,
  Plus,
  Present,
  Box
} from '@element-plus/icons-vue'

const authStore = useAuthStore()

// 统计数据
const stats = ref({
  totalOrders: 0,
  totalRevenue: 0,
  totalProducts: 0,
  totalUsers: 0
})

// 最近订单
const recentOrders = ref([])

// 低库存商品
const lowStockProducts = ref([])

// 获取订单状态类型
const getOrderStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    confirmed: 'info',
    preparing: 'primary',
    ready: 'success',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    // 这里调用API获取统计数据
    // const response = await getDashboardStats()
    
    // 模拟数据
    stats.value = {
      totalOrders: 156,
      totalRevenue: 12580.50,
      totalProducts: 25,
      totalUsers: 89
    }
    
    recentOrders.value = [
      {
        order_no: '240101001',
        user: { username: '张三' },
        final_amount: 45.50,
        status: 'pending'
      },
      {
        order_no: '240101002',
        user: { username: '李四' },
        final_amount: 32.00,
        status: 'confirmed'
      }
    ]
    
    lowStockProducts.value = [
      {
        product: { name: '手工拉面' },
        stock_jin: 5.5,
        min_stock_jin: 10
      },
      {
        product: { name: '芝麻烧饼' },
        stock_jin: 8.0,
        min_stock_jin: 15
      }
    ]
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.orders {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.products {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.users {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.low-stock {
  color: #f56c6c;
  font-weight: 600;
}

.quick-actions {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.quick-action-btn {
  width: 100%;
  height: 60px;
  font-size: 16px;
}
</style>
