const {
  User,
  Category,
  Product,
  Inventory,
  SystemConfig,
  Promotion
} = require('../models');
const { hashPassword } = require('../utils/auth');
require('dotenv').config();

const seedData = async () => {
  try {
    console.log('开始初始化数据...');

    // 创建管理员用户
    const adminPassword = await hashPassword('admin123456');
    const [admin] = await User.findOrCreate({
      where: { username: 'admin' },
      defaults: {
        username: 'admin',
        email: '<EMAIL>',
        password: adminPassword,
        role: 'admin',
        status: 'active'
      }
    });
    console.log('管理员用户创建完成');

    // 创建测试用户
    const userPassword = await hashPassword('user123456');
    const [testUser] = await User.findOrCreate({
      where: { username: 'testuser' },
      defaults: {
        username: 'testuser',
        email: '<EMAIL>',
        password: userPassword,
        phone: '13800138000',
        role: 'user',
        status: 'active'
      }
    });
    console.log('测试用户创建完成');

    // 创建商品分类
    const categories = [
      { name: '手工面条', description: '各种手工制作的新鲜面条', sort_order: 1 },
      { name: '手工面片', description: '传统手工面片，口感劲道', sort_order: 2 },
      { name: '烧饼类', description: '各种口味的传统烧饼', sort_order: 3 },
      { name: '特色小食', description: '店内特色小食和配菜', sort_order: 4 }
    ];

    const createdCategories = [];
    for (const categoryData of categories) {
      const [category] = await Category.findOrCreate({
        where: { name: categoryData.name },
        defaults: categoryData
      });
      createdCategories.push(category);
    }
    console.log('商品分类创建完成');

    // 创建商品
    const products = [
      {
        sku: 'g001',
        name: '手工拉面',
        description: '传统手工拉制，面条劲道爽滑，适合各种汤面',
        price_per_jin: 12.00,
        category_id: createdCategories[0].id,
        sort_order: 1,
        initial_stock: 50
      },
      {
        sku: 'g002',
        name: '手工刀削面',
        description: '现场刀削，厚薄均匀，口感独特',
        price_per_jin: 15.00,
        category_id: createdCategories[0].id,
        sort_order: 2,
        initial_stock: 30
      },
      {
        sku: 'g003',
        name: '手工面片',
        description: '传统手工面片，适合炒制或煮汤',
        price_per_jin: 10.00,
        category_id: createdCategories[1].id,
        sort_order: 1,
        initial_stock: 40
      },
      {
        sku: 'g004',
        name: '芝麻烧饼',
        description: '香脆可口的芝麻烧饼，外酥内软',
        price_per_jin: 8.00,
        category_id: createdCategories[2].id,
        sort_order: 1,
        initial_stock: 60
      },
      {
        sku: 'g005',
        name: '肉夹馍烧饼',
        description: '专门用于制作肉夹馍的烧饼',
        price_per_jin: 9.00,
        category_id: createdCategories[2].id,
        sort_order: 2,
        initial_stock: 45
      }
    ];

    for (const productData of products) {
      const { initial_stock, ...productInfo } = productData;
      
      const [product] = await Product.findOrCreate({
        where: { sku: productData.sku },
        defaults: productInfo
      });

      // 创建库存记录
      await Inventory.findOrCreate({
        where: { product_id: product.id },
        defaults: {
          product_id: product.id,
          stock_jin: initial_stock,
          min_stock_jin: 10
        }
      });
    }
    console.log('商品和库存创建完成');

    // 创建系统配置
    const systemConfigs = [
      {
        config_key: 'shop_name',
        config_value: '老李手工面食店',
        description: '店铺名称'
      },
      {
        config_key: 'shop_address',
        config_value: '北京市朝阳区美食街123号',
        description: '店铺地址'
      },
      {
        config_key: 'shop_phone',
        config_value: '010-12345678',
        description: '店铺电话'
      },
      {
        config_key: 'business_hours',
        config_value: '周一至周日 06:00-22:00',
        description: '营业时间'
      },
      {
        config_key: 'current_promotion',
        config_value: '消费满50元送酱油醋',
        description: '当前促销活动'
      }
    ];

    for (const configData of systemConfigs) {
      await SystemConfig.findOrCreate({
        where: { config_key: configData.config_key },
        defaults: configData
      });
    }
    console.log('系统配置创建完成');

    // 创建促销活动
    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    const promotions = [
      {
        title: '消费满50元送酱油醋',
        description: '单笔消费满50元即可获赠精装酱油醋一套',
        type: 'gift',
        condition_amount: 50.00,
        gift_description: '精装酱油醋一套',
        start_time: now,
        end_time: nextWeek,
        status: 'active'
      },
      {
        title: '新用户首单9折',
        description: '新注册用户首次下单享受9折优惠',
        type: 'discount',
        discount_value: 10.00, // 10%折扣
        start_time: now,
        end_time: nextWeek,
        status: 'active'
      },
      {
        title: '满100减20',
        description: '单笔消费满100元立减20元',
        type: 'full_reduction',
        condition_amount: 100.00,
        discount_value: 20.00,
        start_time: tomorrow,
        end_time: nextWeek,
        status: 'active'
      }
    ];

    for (const promotionData of promotions) {
      await Promotion.findOrCreate({
        where: { title: promotionData.title },
        defaults: promotionData
      });
    }
    console.log('促销活动创建完成');

    console.log('数据初始化完成！');
    console.log('管理员账号: admin / admin123456');
    console.log('测试用户账号: testuser / user123456');

  } catch (error) {
    console.error('数据初始化失败:', error);
    process.exit(1);
  }
};

// 如果直接运行此文件，则执行数据初始化
if (require.main === module) {
  seedData().then(() => {
    process.exit(0);
  });
}

module.exports = seedData;
