<template>
  <div class="shop-layout">
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-content">
          <div class="logo">
            <h1>老李手工面食店</h1>
          </div>
          
          <el-menu
            :default-active="$route.path"
            mode="horizontal"
            router
            class="nav-menu"
          >
            <el-menu-item index="/shop">首页</el-menu-item>
            <el-menu-item index="/shop/products">商品</el-menu-item>
          </el-menu>
          
          <div class="header-right">
            <!-- 天气信息 -->
            <div class="weather-info" v-if="weather">
              <el-icon><Sunny /></el-icon>
              <span>{{ weather.city }} {{ weather.temperature }}°C</span>
            </div>
            
            <!-- 用户菜单 -->
            <div class="user-actions" v-if="authStore.isLoggedIn">
              <el-badge :value="cartStore.itemCount > 0 ? cartStore.itemCount.toFixed(1) : ''" :hidden="cartStore.itemCount === 0">
                <el-button type="text" @click="$router.push('/shop/cart')">
                  <el-icon><ShoppingCart /></el-icon>
                  购物车
                </el-button>
              </el-badge>
              
              <el-dropdown @command="handleUserCommand">
                <div class="user-info">
                  <el-avatar :src="authStore.user?.avatar" :size="32">
                    {{ authStore.user?.username?.charAt(0) }}
                  </el-avatar>
                  <span>{{ authStore.user?.username }}</span>
                  <el-icon><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="orders">我的订单</el-dropdown-item>
                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                    <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <div class="auth-actions" v-else>
              <el-button type="text" @click="$router.push('/login')">
                登录
              </el-button>
            </div>
          </div>
        </div>
      </el-header>
      
      <!-- 主内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
      
      <!-- 底部 -->
      <el-footer class="footer">
        <div class="footer-content">
          <p>&copy; 2024 老李手工面食店. 保留所有权利.</p>
          <p>地址：北京市朝阳区美食街123号 | 电话：010-12345678</p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useCartStore } from '@/stores/cart'
import { ElMessage } from 'element-plus'
import {
  Sunny,
  ShoppingCart,
  ArrowDown
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const cartStore = useCartStore()

// 天气信息
const weather = ref<any>(null)

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'orders':
      router.push('/shop/orders')
      break
    case 'profile':
      router.push('/shop/profile')
      break
    case 'logout':
      authStore.logout()
      router.push('/shop')
      ElMessage.success('已退出登录')
      break
  }
}
</script>

<style scoped>
.shop-layout {
  min-height: 100vh;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  color: #409eff;
  font-weight: 600;
}

.nav-menu {
  border: none;
  flex: 1;
  margin: 0 40px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.main-content {
  min-height: calc(100vh - 120px);
  background-color: #f5f7fa;
  padding: 20px;
}

.footer {
  background-color: #303133;
  color: white;
  text-align: center;
  padding: 20px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-content p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
