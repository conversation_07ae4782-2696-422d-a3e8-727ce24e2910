// API响应类型定义

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  timestamp: string
}

export interface PaginatedResponse<T = any> {
  success: boolean
  message: string
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  timestamp: string
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  phone?: string
  role: 'admin' | 'user'
  status: 'active' | 'inactive'
  avatar?: string
  createdAt: string
  updatedAt: string
}

// 分类类型
export interface Category {
  id: number
  name: string
  description?: string
  sort_order: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  products?: Product[]
}

// 商品类型
export interface Product {
  id: number
  sku: string
  name: string
  description?: string
  price_per_jin: number
  images?: string[]
  category_id: number
  status: 'active' | 'inactive'
  sort_order: number
  createdAt: string
  updatedAt: string
  category?: Category
  inventory?: Inventory
}

// 库存类型
export interface Inventory {
  id: number
  product_id: number
  stock_jin: number
  min_stock_jin: number
  last_updated: string
  createdAt: string
  updatedAt: string
  product?: Product
}

// 促销活动类型
export interface Promotion {
  id: number
  title: string
  description?: string
  type: 'discount' | 'gift' | 'full_reduction'
  condition_amount?: number
  discount_value?: number
  gift_description?: string
  start_time: string
  end_time: string
  status: 'active' | 'inactive' | 'expired'
  createdAt: string
  updatedAt: string
}

// 订单类型
export interface Order {
  id: number
  order_no: string
  user_id: number
  total_amount: number
  discount_amount: number
  final_amount: number
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled'
  pickup_time?: string
  notes?: string
  promotion_id?: number
  createdAt: string
  updatedAt: string
  user?: User
  promotion?: Promotion
  items?: OrderItem[]
}

// 订单项类型
export interface OrderItem {
  id: number
  order_id: number
  product_id: number
  quantity_jin: number
  unit_price: number
  subtotal: number
  createdAt: string
  updatedAt: string
  product?: Product
}

// 天气信息类型
export interface Weather {
  city: string
  country: string
  temperature: number
  feels_like: number
  humidity: number
  pressure: number
  description: string
  icon: string
  wind_speed: number
  wind_direction: number
  visibility?: number
  sunrise: string
  sunset: string
  timestamp: string
}

// 查询参数类型
export interface QueryParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  [key: string]: any
}
