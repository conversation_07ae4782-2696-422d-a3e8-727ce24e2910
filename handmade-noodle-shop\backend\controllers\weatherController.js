const axios = require('axios');
const response = require('../utils/response');

// 获取天气信息
const getWeather = async (req, res) => {
  try {
    const { city = 'Beijing', country = 'CN' } = req.query;

    // 检查是否配置了天气API密钥
    if (!process.env.WEATHER_API_KEY || process.env.WEATHER_API_KEY === 'your_openweathermap_api_key_here') {
      return response.error(res, '天气API密钥未配置', 503);
    }

    // 调用OpenWeatherMap API
    const weatherResponse = await axios.get(process.env.WEATHER_API_URL, {
      params: {
        q: `${city},${country}`,
        appid: process.env.WEATHER_API_KEY,
        units: 'metric', // 使用摄氏度
        lang: 'zh_cn'    // 中文描述
      },
      timeout: 5000 // 5秒超时
    });

    const weatherData = weatherResponse.data;

    // 格式化天气数据
    const formattedWeather = {
      city: weatherData.name,
      country: weatherData.sys.country,
      temperature: Math.round(weatherData.main.temp),
      feels_like: Math.round(weatherData.main.feels_like),
      humidity: weatherData.main.humidity,
      pressure: weatherData.main.pressure,
      description: weatherData.weather[0].description,
      icon: weatherData.weather[0].icon,
      wind_speed: weatherData.wind.speed,
      wind_direction: weatherData.wind.deg,
      visibility: weatherData.visibility ? weatherData.visibility / 1000 : null, // 转换为公里
      sunrise: new Date(weatherData.sys.sunrise * 1000).toLocaleTimeString('zh-CN'),
      sunset: new Date(weatherData.sys.sunset * 1000).toLocaleTimeString('zh-CN'),
      timestamp: new Date().toISOString()
    };

    response.success(res, formattedWeather, '获取天气信息成功');

  } catch (error) {
    console.error('获取天气信息错误:', error);
    
    if (error.response) {
      // API返回错误
      const status = error.response.status;
      if (status === 401) {
        return response.error(res, '天气API密钥无效', 401);
      } else if (status === 404) {
        return response.error(res, '城市不存在', 404);
      } else {
        return response.error(res, '天气服务暂时不可用', 503);
      }
    } else if (error.code === 'ECONNABORTED') {
      return response.error(res, '天气服务请求超时', 408);
    } else {
      return response.serverError(res, '获取天气信息失败');
    }
  }
};

// 根据IP获取城市信息（简化版本）
const getCityByIP = async (req, res) => {
  try {
    // 获取客户端IP
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null);

    // 简化处理：如果是本地IP，返回默认城市
    if (!clientIP || clientIP === '127.0.0.1' || clientIP === '::1' || clientIP.includes('192.168.')) {
      return response.success(res, {
        city: 'Beijing',
        country: 'CN',
        ip: clientIP || 'unknown'
      }, '获取城市信息成功（默认）');
    }

    // 这里可以集成IP地理位置服务，如ipapi.co、ip-api.com等
    // 为了简化，我们返回默认城市
    response.success(res, {
      city: 'Beijing',
      country: 'CN',
      ip: clientIP
    }, '获取城市信息成功');

  } catch (error) {
    console.error('获取城市信息错误:', error);
    response.serverError(res, '获取城市信息失败');
  }
};

// 获取天气图标URL
const getWeatherIconUrl = (iconCode) => {
  return `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
};

// 天气状况中文映射
const getWeatherDescriptionCN = (weatherCode) => {
  const weatherMap = {
    '01d': '晴天',
    '01n': '晴夜',
    '02d': '少云',
    '02n': '少云',
    '03d': '多云',
    '03n': '多云',
    '04d': '阴天',
    '04n': '阴天',
    '09d': '阵雨',
    '09n': '阵雨',
    '10d': '小雨',
    '10n': '小雨',
    '11d': '雷雨',
    '11n': '雷雨',
    '13d': '雪',
    '13n': '雪',
    '50d': '雾',
    '50n': '雾'
  };
  
  return weatherMap[weatherCode] || '未知';
};

module.exports = {
  getWeather,
  getCityByIP,
  getWeatherIconUrl,
  getWeatherDescriptionCN
};
