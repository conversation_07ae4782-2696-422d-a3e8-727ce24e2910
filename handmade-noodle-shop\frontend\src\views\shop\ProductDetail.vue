<template>
  <div class="product-detail">
    <div class="page-container" v-loading="loading">
      <div v-if="product" class="product-content">
        <!-- 面包屑导航 -->
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/shop' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/shop/products' }">商品列表</el-breadcrumb-item>
          <el-breadcrumb-item>{{ product.name }}</el-breadcrumb-item>
        </el-breadcrumb>

        <div class="product-main">
          <!-- 商品图片 -->
          <div class="product-images">
            <div class="main-image">
              <img
                :src="currentImage || '/placeholder.jpg'"
                :alt="product.name"
              />
            </div>
            <div class="thumbnail-list" v-if="product.images && product.images.length > 1">
              <div
                v-for="(image, index) in product.images"
                :key="index"
                class="thumbnail"
                :class="{ active: currentImage === image }"
                @click="currentImage = image"
              >
                <img :src="image" :alt="`${product.name} ${index + 1}`" />
              </div>
            </div>
          </div>

          <!-- 商品信息 -->
          <div class="product-info">
            <h1 class="product-title">{{ product.name }}</h1>
            <div class="product-meta">
              <span class="product-sku">SKU: {{ product.sku }}</span>
              <span class="product-category">分类: {{ product.category?.name }}</span>
            </div>

            <div class="product-price">
              <span class="price">¥{{ product.price_per_jin }}</span>
              <span class="unit">/斤</span>
            </div>

            <div class="product-stock">
              <span class="stock-label">库存:</span>
              <span
                class="stock-value"
                :class="{ 'low-stock': product.inventory && product.inventory.stock_jin <= product.inventory.min_stock_jin }"
              >
                {{ product.inventory ? product.inventory.stock_jin : 0 }}斤
              </span>
            </div>

            <div class="product-description">
              <h3>商品描述</h3>
              <p>{{ product.description || '暂无描述' }}</p>
            </div>

            <!-- 购买选项 -->
            <div class="purchase-options">
              <div class="quantity-selector">
                <span class="quantity-label">购买数量:</span>
                <el-input-number
                  v-model="quantity"
                  :min="0.5"
                  :max="maxQuantity"
                  :step="0.5"
                  :precision="1"
                  size="large"
                />
                <span class="quantity-unit">斤</span>
              </div>

              <div class="total-price">
                <span class="total-label">小计:</span>
                <span class="total-value">¥{{ totalPrice }}</span>
              </div>

              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="large"
                  @click="addToCart"
                  :disabled="!canPurchase"
                  class="add-to-cart-btn"
                >
                  <el-icon><ShoppingCart /></el-icon>
                  加入购物车
                </el-button>

                <el-button
                  type="success"
                  size="large"
                  @click="buyNow"
                  :disabled="!canPurchase"
                  class="buy-now-btn"
                >
                  立即购买
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关商品推荐 -->
        <div class="related-products" v-if="relatedProducts.length > 0">
          <h2>相关商品推荐</h2>
          <div class="related-grid">
            <div
              v-for="relatedProduct in relatedProducts"
              :key="relatedProduct.id"
              class="related-item"
              @click="goToProduct(relatedProduct.id)"
            >
              <img
                :src="relatedProduct.images?.[0] || '/placeholder.jpg'"
                :alt="relatedProduct.name"
              />
              <h4>{{ relatedProduct.name }}</h4>
              <p class="related-price">¥{{ relatedProduct.price_per_jin }}/斤</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品不存在 -->
      <div v-else-if="!loading" class="not-found">
        <el-result
          icon="warning"
          title="商品不存在"
          sub-title="您访问的商品可能已下架或不存在"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/shop/products')">
              返回商品列表
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ShoppingCart } from '@element-plus/icons-vue'
import { getProductById } from '@/api/products'
import type { Product } from '@/api/types'

const route = useRoute()
const router = useRouter()

// 数据
const product = ref<Product | null>(null)
const relatedProducts = ref<Product[]>([])
const loading = ref(false)
const quantity = ref(1)
const currentImage = ref('')

// 计算属性
const maxQuantity = computed(() => {
  return product.value?.inventory?.stock_jin || 0
})

const totalPrice = computed(() => {
  if (!product.value) return '0.00'
  return (product.value.price_per_jin * quantity.value).toFixed(2)
})

const canPurchase = computed(() => {
  return product.value &&
         product.value.inventory &&
         product.value.inventory.stock_jin > 0 &&
         quantity.value > 0 &&
         quantity.value <= maxQuantity.value
})

// 加载商品详情
const loadProduct = async () => {
  const productId = parseInt(route.params.id as string)
  if (!productId) return

  loading.value = true
  try {
    const response = await getProductById(productId)
    product.value = response.data

    // 设置默认图片
    if (product.value.images && product.value.images.length > 0) {
      currentImage.value = product.value.images[0]
    }

    // 加载相关商品
    loadRelatedProducts()
  } catch (error) {
    console.error('加载商品详情失败:', error)
    ElMessage.error('加载商品详情失败')
  } finally {
    loading.value = false
  }
}

// 加载相关商品
const loadRelatedProducts = async () => {
  // 这里应该根据分类或其他条件加载相关商品
  // 暂时使用模拟数据
  relatedProducts.value = []
}

// 添加到购物车
const addToCart = () => {
  if (!canPurchase.value) return

  // 这里应该实现添加到购物车的逻辑
  ElMessage.success(`已添加 ${quantity.value}斤 ${product.value?.name} 到购物车`)
}

// 立即购买
const buyNow = () => {
  if (!canPurchase.value) return

  // 这里应该跳转到结算页面
  ElMessage.info('立即购买功能开发中...')
}

// 跳转到其他商品
const goToProduct = (productId: number) => {
  router.push(`/shop/products/${productId}`)
}

// 监听路由变化
watch(() => route.params.id, () => {
  loadProduct()
}, { immediate: true })

onMounted(() => {
  loadProduct()
})
</script>

<style scoped>
.product-detail {
  min-height: calc(100vh - 200px);
  background-color: #f5f7fa;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.breadcrumb {
  margin-bottom: 20px;
}

.product-content {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.product-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.main-image {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-list {
  display: flex;
  gap: 8px;
  overflow-x: auto;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid transparent;
  cursor: pointer;
  transition: border-color 0.3s;
}

.thumbnail.active {
  border-color: #409eff;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.product-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #909399;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price {
  font-size: 32px;
  font-weight: 600;
  color: #f56c6c;
}

.unit {
  font-size: 16px;
  color: #909399;
}

.product-stock {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.stock-label {
  color: #606266;
}

.stock-value {
  font-weight: 600;
  color: #67c23a;
}

.stock-value.low-stock {
  color: #f56c6c;
}

.product-description h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #303133;
}

.product-description p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.purchase-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.quantity-label {
  font-weight: 500;
  color: #303133;
}

.quantity-unit {
  color: #909399;
}

.total-price {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
}

.total-label {
  color: #606266;
}

.total-value {
  font-size: 24px;
  font-weight: 600;
  color: #f56c6c;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.add-to-cart-btn,
.buy-now-btn {
  flex: 1;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.related-products {
  margin-top: 40px;
  padding-top: 40px;
  border-top: 1px solid #e4e7ed;
}

.related-products h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #303133;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.related-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid #e4e7ed;
}

.related-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.related-item img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 12px;
}

.related-item h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
}

.not-found {
  text-align: center;
  padding: 60px 0;
}

@media (max-width: 768px) {
  .product-main {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .related-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
</style>
