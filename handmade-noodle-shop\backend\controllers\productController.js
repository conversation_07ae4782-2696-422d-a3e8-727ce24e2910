const { Product, Category, Inventory, OrderItem } = require('../models');
const { Op } = require('sequelize');
const response = require('../utils/response');

// 获取商品列表
const getProducts = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      category_id, 
      status = 'active',
      search,
      sort_by = 'createdAt',
      sort_order = 'DESC'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    const whereClause = {};
    
    if (status !== 'all') {
      whereClause.status = status;
    }
    
    if (category_id) {
      whereClause.category_id = category_id;
    }
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { sku: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 查询商品
    const { count, rows: products } = await Product.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: Inventory,
          as: 'inventory',
          attributes: ['stock_jin', 'min_stock_jin']
        }
      ],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset
    });

    response.paginated(res, products, {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count
    }, '获取商品列表成功');

  } catch (error) {
    console.error('获取商品列表错误:', error);
    response.serverError(res, '获取商品列表失败');
  }
};

// 获取单个商品
const getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'description']
        },
        {
          model: Inventory,
          as: 'inventory',
          attributes: ['stock_jin', 'min_stock_jin', 'last_updated']
        }
      ]
    });

    if (!product) {
      return response.notFound(res, '商品不存在');
    }

    response.success(res, product, '获取商品成功');
  } catch (error) {
    console.error('获取商品错误:', error);
    response.serverError(res, '获取商品失败');
  }
};

// 创建商品（管理员）
const createProduct = async (req, res) => {
  try {
    const { 
      sku, 
      name, 
      description, 
      price_per_jin, 
      images, 
      category_id,
      sort_order = 0,
      initial_stock = 0
    } = req.body;

    // 数据验证
    const errors = [];
    
    if (!sku || !sku.startsWith('g')) {
      errors.push('SKU必须以g开头');
    }
    
    if (!name || name.trim().length === 0) {
      errors.push('商品名称不能为空');
    }
    
    if (!price_per_jin || price_per_jin <= 0) {
      errors.push('价格必须大于0');
    }
    
    if (!category_id) {
      errors.push('必须选择商品分类');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    // 检查SKU是否已存在
    const existingSku = await Product.findOne({ where: { sku } });
    if (existingSku) {
      return response.error(res, 'SKU已存在', 409);
    }

    // 检查分类是否存在
    const category = await Category.findByPk(category_id);
    if (!category) {
      return response.error(res, '商品分类不存在', 404);
    }

    // 创建商品
    const product = await Product.create({
      sku,
      name: name.trim(),
      description: description?.trim(),
      price_per_jin: parseFloat(price_per_jin),
      images: images || [],
      category_id: parseInt(category_id),
      sort_order: parseInt(sort_order) || 0
    });

    // 创建库存记录
    await Inventory.create({
      product_id: product.id,
      stock_jin: parseFloat(initial_stock) || 0,
      min_stock_jin: 0
    });

    // 获取完整的商品信息
    const fullProduct = await Product.findByPk(product.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: Inventory,
          as: 'inventory',
          attributes: ['stock_jin', 'min_stock_jin']
        }
      ]
    });

    response.success(res, fullProduct, '创建商品成功', 201);
  } catch (error) {
    console.error('创建商品错误:', error);
    response.serverError(res, '创建商品失败');
  }
};

// 更新商品（管理员）
const updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      sku, 
      name, 
      description, 
      price_per_jin, 
      images, 
      category_id,
      sort_order,
      status
    } = req.body;

    const product = await Product.findByPk(id);
    if (!product) {
      return response.notFound(res, '商品不存在');
    }

    // 数据验证
    const errors = [];
    
    if (sku && !sku.startsWith('g')) {
      errors.push('SKU必须以g开头');
    }
    
    if (name && name.trim().length === 0) {
      errors.push('商品名称不能为空');
    }
    
    if (price_per_jin && price_per_jin <= 0) {
      errors.push('价格必须大于0');
    }
    
    if (status && !['active', 'inactive'].includes(status)) {
      errors.push('状态值无效');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    // 检查SKU是否已被其他商品使用
    if (sku && sku !== product.sku) {
      const existingSku = await Product.findOne({
        where: { 
          sku,
          id: { [Op.ne]: id }
        }
      });
      if (existingSku) {
        return response.error(res, 'SKU已存在', 409);
      }
    }

    // 检查分类是否存在
    if (category_id) {
      const category = await Category.findByPk(category_id);
      if (!category) {
        return response.error(res, '商品分类不存在', 404);
      }
    }

    // 更新商品
    const updateData = {};
    if (sku) updateData.sku = sku;
    if (name) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim();
    if (price_per_jin) updateData.price_per_jin = parseFloat(price_per_jin);
    if (images !== undefined) updateData.images = images;
    if (category_id) updateData.category_id = parseInt(category_id);
    if (sort_order !== undefined) updateData.sort_order = parseInt(sort_order) || 0;
    if (status) updateData.status = status;

    await product.update(updateData);

    // 获取更新后的商品信息
    const updatedProduct = await Product.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: Inventory,
          as: 'inventory',
          attributes: ['stock_jin', 'min_stock_jin']
        }
      ]
    });

    response.success(res, updatedProduct, '更新商品成功');
  } catch (error) {
    console.error('更新商品错误:', error);
    response.serverError(res, '更新商品失败');
  }
};

// 删除商品（管理员）
const deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);
    if (!product) {
      return response.notFound(res, '商品不存在');
    }

    // 检查是否有订单项使用此商品
    const orderItemCount = await OrderItem.count({
      where: { product_id: id }
    });

    if (orderItemCount > 0) {
      return response.error(res, '该商品已被订单使用，无法删除', 409);
    }

    // 删除相关的库存记录
    await Inventory.destroy({ where: { product_id: id } });

    // 删除商品
    await product.destroy();

    response.success(res, null, '删除商品成功');
  } catch (error) {
    console.error('删除商品错误:', error);
    response.serverError(res, '删除商品失败');
  }
};

module.exports = {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct
};
