// 认证相关工具函数

// Token存储键名
export const TOKEN_KEY = 'noodle_shop_token'
export const USER_KEY = 'noodle_shop_user'

// 获取Token
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY)
}

// 设置Token
export function setToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token)
}

// 移除Token
export function removeToken(): void {
  localStorage.removeItem(TOKEN_KEY)
}

// 获取用户信息
export function getUser(): any | null {
  const userStr = localStorage.getItem(USER_KEY)
  return userStr ? JSON.parse(userStr) : null
}

// 设置用户信息
export function setUser(user: any): void {
  localStorage.setItem(USER_KEY, JSON.stringify(user))
}

// 移除用户信息
export function removeUser(): void {
  localStorage.removeItem(USER_KEY)
}

// 检查是否已登录
export function isLoggedIn(): boolean {
  return !!getToken()
}

// 检查是否是管理员
export function isAdmin(): boolean {
  const user = getUser()
  return user?.role === 'admin'
}

// 清除所有认证信息
export function clearAuth(): void {
  removeToken()
  removeUser()
}
