const express = require('express');
const router = express.Router();
const promotionController = require('../controllers/promotionController');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');

// 获取促销活动列表（公开，但管理员可以看到更多信息）
router.get('/', optionalAuth, promotionController.getPromotions);

// 获取单个促销活动（公开）
router.get('/:id', promotionController.getPromotionById);

// 检查促销活动有效性（公开）
router.get('/:id/check', promotionController.checkPromotionValidity);

// 创建促销活动（管理员）
router.post('/', authenticateToken, requireAdmin, promotionController.createPromotion);

// 更新促销活动（管理员）
router.put('/:id', authenticateToken, requireAdmin, promotionController.updatePromotion);

// 删除促销活动（管理员）
router.delete('/:id', authenticateToken, requireAdmin, promotionController.deletePromotion);

module.exports = router;
