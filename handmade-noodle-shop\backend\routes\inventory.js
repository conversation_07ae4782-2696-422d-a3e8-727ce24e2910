const express = require('express');
const router = express.Router();
const inventoryController = require('../controllers/inventoryController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 获取库存列表（管理员）
router.get('/', authenticateToken, requireAdmin, inventoryController.getInventory);

// 获取单个商品库存（管理员）
router.get('/product/:productId', authenticateToken, requireAdmin, inventoryController.getInventoryByProductId);

// 更新库存（管理员）
router.put('/product/:productId', authenticateToken, requireAdmin, inventoryController.updateInventory);

// 批量更新库存（管理员）
router.put('/batch', authenticateToken, requireAdmin, inventoryController.batchUpdateInventory);

// 获取低库存商品（管理员）
router.get('/low-stock', authenticateToken, requireAdmin, inventoryController.getLowStockProducts);

module.exports = router;
