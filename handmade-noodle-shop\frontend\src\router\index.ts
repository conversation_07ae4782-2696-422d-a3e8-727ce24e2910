import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/admin'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: {
        title: '管理后台',
        requiresAuth: true,
        requiresAdmin: true
      },
      children: [
        {
          path: '',
          redirect: '/admin/dashboard'
        },
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/admin/Dashboard.vue'),
          meta: {
            title: '仪表盘',
            icon: 'Dashboard'
          }
        },
        {
          path: 'categories',
          name: 'Categories',
          component: () => import('@/views/admin/Categories.vue'),
          meta: {
            title: '分类管理',
            icon: 'Menu'
          }
        },
        {
          path: 'products',
          name: 'Products',
          component: () => import('@/views/admin/Products.vue'),
          meta: {
            title: '商品管理',
            icon: 'Goods'
          }
        },
        {
          path: 'inventory',
          name: 'Inventory',
          component: () => import('@/views/admin/Inventory.vue'),
          meta: {
            title: '库存管理',
            icon: 'Box'
          }
        },
        {
          path: 'promotions',
          name: 'Promotions',
          component: () => import('@/views/admin/Promotions.vue'),
          meta: {
            title: '促销管理',
            icon: 'Present'
          }
        },
        {
          path: 'orders',
          name: 'Orders',
          component: () => import('@/views/admin/Orders.vue'),
          meta: {
            title: '订单管理',
            icon: 'Document'
          }
        },
        {
          path: 'users',
          name: 'Users',
          component: () => import('@/views/admin/Users.vue'),
          meta: {
            title: '用户管理',
            icon: 'User'
          }
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/views/admin/Settings.vue'),
          meta: {
            title: '系统设置',
            icon: 'Setting'
          }
        }
      ]
    },
    {
      path: '/shop',
      name: 'Shop',
      component: () => import('@/layouts/ShopLayout.vue'),
      meta: {
        title: '手工面食店',
        requiresAuth: false
      },
      children: [
        {
          path: '',
          name: 'ShopHome',
          component: () => import('@/views/shop/Home.vue'),
          meta: {
            title: '首页'
          }
        },
        {
          path: 'products',
          name: 'ShopProducts',
          component: () => import('@/views/shop/Products.vue'),
          meta: {
            title: '商品列表'
          }
        },
        {
          path: 'products/:id',
          name: 'ProductDetail',
          component: () => import('@/views/shop/ProductDetail.vue'),
          meta: {
            title: '商品详情'
          }
        },
        {
          path: 'cart',
          name: 'Cart',
          component: () => import('@/views/shop/Cart.vue'),
          meta: {
            title: '购物车',
            requiresAuth: true
          }
        },
        {
          path: 'orders',
          name: 'UserOrders',
          component: () => import('@/views/shop/Orders.vue'),
          meta: {
            title: '我的订单',
            requiresAuth: true
          }
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/shop/Profile.vue'),
          meta: {
            title: '个人中心',
            requiresAuth: true
          }
        },
        {
          path: 'checkout',
          name: 'Checkout',
          component: () => import('@/views/shop/Checkout.vue'),
          meta: {
            title: '确认订单',
            requiresAuth: true
          }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue'),
      meta: {
        title: '页面不存在'
      }
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 手工面食店` : '手工面食店'
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isLoggedIn) {
      ElMessage.warning('请先登录')
      next('/login')
      return
    }
    
    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      ElMessage.error('需要管理员权限')
      next('/shop')
      return
    }
  }
  
  // 如果已登录且访问登录页，重定向到相应页面
  if (to.path === '/login' && authStore.isLoggedIn) {
    if (authStore.isAdmin) {
      next('/admin')
    } else {
      next('/shop')
    }
    return
  }
  
  next()
})

export default router
