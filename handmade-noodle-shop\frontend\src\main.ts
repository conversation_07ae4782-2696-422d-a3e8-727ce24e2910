import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 配置路由
import router from './router'

// 配置状态管理
const pinia = createPinia()

app.use(ElementPlus, {
  locale: zhCn,
})
app.use(pinia)
app.use(router)

// 初始化认证状态
import { useAuthStore } from './stores/auth'
import { useCartStore } from './stores/cart'
const authStore = useAuthStore()
const cartStore = useCartStore()
authStore.initAuth()
cartStore.initCart()

app.mount('#app')
