import request from '@/utils/request'
import type { ApiResponse, PaginatedResponse, Product, QueryParams } from './types'

export interface ProductForm {
  sku: string
  name: string
  description?: string
  price_per_jin: number
  images?: string[]
  category_id: number
  sort_order?: number
  initial_stock?: number
}

// 获取商品列表
export function getProducts(params?: QueryParams): Promise<PaginatedResponse<Product>> {
  return request.get('/products', { params })
}

// 获取单个商品
export function getProductById(id: number): Promise<ApiResponse<Product>> {
  return request.get(`/products/${id}`)
}

// 创建商品
export function createProduct(data: ProductForm): Promise<ApiResponse<Product>> {
  return request.post('/products', data)
}

// 更新商品
export function updateProduct(id: number, data: Partial<ProductForm>): Promise<ApiResponse<Product>> {
  return request.put(`/products/${id}`, data)
}

// 删除商品
export function deleteProduct(id: number): Promise<ApiResponse<null>> {
  return request.delete(`/products/${id}`)
}
