<template>
  <div class="cart-page">
    <div class="page-container">
      <div class="page-header">
        <h1>购物车</h1>
        <div class="cart-count">
          共 {{ cartStore.itemCount.toFixed(1) }} 斤商品
        </div>
      </div>

      <!-- 购物车内容 -->
      <div v-if="!cartStore.isEmpty" class="cart-content">
        <!-- 购物车列表 -->
        <div class="cart-list">
          <div class="cart-header">
            <div class="item-info">商品信息</div>
            <div class="item-price">单价</div>
            <div class="item-quantity">数量</div>
            <div class="item-subtotal">小计</div>
            <div class="item-actions">操作</div>
          </div>

          <div
            v-for="item in cartStore.items"
            :key="item.id"
            class="cart-item"
          >
            <div class="item-info">
              <div class="product-image">
                <img
                  :src="item.product.images?.[0] || '/placeholder.jpg'"
                  :alt="item.product.name"
                />
              </div>
              <div class="product-details">
                <h3 class="product-name">{{ item.product.name }}</h3>
                <p class="product-sku">SKU: {{ item.product.sku }}</p>
                <p class="product-category">{{ item.product.category?.name }}</p>
              </div>
            </div>

            <div class="item-price">
              ¥{{ item.unit_price.toFixed(2) }}/斤
            </div>

            <div class="item-quantity">
              <el-input-number
                :model-value="item.quantity"
                @update:model-value="(value) => updateQuantity(item.id, value)"
                :min="0.5"
                :max="getMaxQuantity(item.product)"
                :step="0.5"
                :precision="1"
                size="small"
              />
              <span class="quantity-unit">斤</span>
            </div>

            <div class="item-subtotal">
              ¥{{ item.subtotal.toFixed(2) }}
            </div>

            <div class="item-actions">
              <el-button
                type="text"
                danger
                @click="removeItem(item.id)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 购物车汇总 -->
        <div class="cart-summary">
          <div class="summary-content">
            <div class="summary-row">
              <span>商品总额：</span>
              <span class="amount">¥{{ cartStore.totalAmount.toFixed(2) }}</span>
            </div>

            <div class="summary-row" v-if="promotionDiscount > 0">
              <span>促销优惠：</span>
              <span class="discount">-¥{{ promotionDiscount.toFixed(2) }}</span>
            </div>

            <div class="summary-row total">
              <span>应付总额：</span>
              <span class="total-amount">¥{{ finalAmount.toFixed(2) }}</span>
            </div>

            <div class="summary-actions">
              <el-button @click="clearCart" size="large">
                清空购物车
              </el-button>
              <el-button
                type="primary"
                size="large"
                @click="goToCheckout"
                :disabled="cartStore.isEmpty"
              >
                去结算
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空购物车 -->
      <div v-else class="empty-cart">
        <el-empty description="购物车是空的">
          <el-button type="primary" @click="$router.push('/shop/products')">
            去购物
          </el-button>
        </el-empty>
      </div>

      <!-- 推荐商品 -->
      <div class="recommended-products" v-if="recommendedProducts.length > 0">
        <h2>为您推荐</h2>
        <div class="products-grid">
          <div
            v-for="product in recommendedProducts"
            :key="product.id"
            class="product-card"
            @click="goToProduct(product.id)"
          >
            <img
              :src="product.images?.[0] || '/placeholder.jpg'"
              :alt="product.name"
            />
            <h4>{{ product.name }}</h4>
            <p class="product-price">¥{{ product.price_per_jin }}/斤</p>
            <el-button
              type="primary"
              size="small"
              @click.stop="addToCart(product)"
            >
              加入购物车
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCartStore } from '@/stores/cart'
import type { Product } from '@/api/types'

const router = useRouter()
const cartStore = useCartStore()

// 数据
const recommendedProducts = ref<Product[]>([])
const promotionDiscount = ref(0)

// 计算属性
const finalAmount = computed(() => {
  return Math.max(0, cartStore.totalAmount - promotionDiscount.value)
})

// 获取商品最大可购买数量
const getMaxQuantity = (product: Product) => {
  return product.inventory?.stock_jin || 0
}

// 更新商品数量
const updateQuantity = (itemId: string, quantity: number) => {
  if (quantity <= 0) {
    removeItem(itemId)
  } else {
    cartStore.updateQuantity(itemId, quantity)
    checkPromotions()
  }
}

// 移除商品
const removeItem = async (itemId: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个商品吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    cartStore.removeItem(itemId)
    checkPromotions()
    ElMessage.success('商品已删除')
  } catch (error) {
    // 用户取消删除
  }
}

// 清空购物车
const clearCart = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空购物车吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    cartStore.clearCart()
    promotionDiscount.value = 0
    ElMessage.success('购物车已清空')
  } catch (error) {
    // 用户取消清空
  }
}

// 检查促销活动
const checkPromotions = () => {
  // 这里应该调用API检查促销活动
  // 暂时使用模拟逻辑
  const totalAmount = cartStore.totalAmount

  if (totalAmount >= 50) {
    // 满50减5的促销
    promotionDiscount.value = 5
  } else {
    promotionDiscount.value = 0
  }
}

// 跳转到结算页面
const goToCheckout = () => {
  if (cartStore.isEmpty) {
    ElMessage.warning('购物车是空的')
    return
  }

  router.push('/shop/checkout')
}

// 跳转到商品详情
const goToProduct = (productId: number) => {
  router.push(`/shop/products/${productId}`)
}

// 添加推荐商品到购物车
const addToCart = (product: Product) => {
  cartStore.addItem(product, 1)
  ElMessage.success(`${product.name} 已添加到购物车`)
  checkPromotions()
}

// 加载推荐商品
const loadRecommendedProducts = async () => {
  // 这里应该调用API加载推荐商品
  // 暂时使用模拟数据
  recommendedProducts.value = []
}

onMounted(() => {
  checkPromotions()
  loadRecommendedProducts()
})
</script>

<style scoped>
.cart-page {
  min-height: calc(100vh - 200px);
  background-color: #f5f7fa;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.cart-count {
  font-size: 16px;
  color: #909399;
}

.cart-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
}

.cart-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.cart-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 80px;
  gap: 20px;
  padding: 16px 20px;
  background-color: #f5f7fa;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
}

.cart-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 80px;
  gap: 20px;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.product-sku,
.product-category {
  margin: 0 0 2px 0;
  font-size: 12px;
  color: #909399;
}

.item-price {
  font-size: 16px;
  color: #f56c6c;
  font-weight: 600;
}

.item-quantity {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-unit {
  font-size: 14px;
  color: #909399;
}

.item-subtotal {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.cart-summary {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 20px;
}

.summary-content {
  padding: 20px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 16px;
}

.summary-row.total {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-top: 1px solid #e4e7ed;
  padding-top: 12px;
  margin-top: 12px;
}

.amount {
  color: #606266;
}

.discount {
  color: #f56c6c;
}

.total-amount {
  color: #f56c6c;
  font-size: 20px;
}

.summary-actions {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-actions .el-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.empty-cart {
  text-align: center;
  padding: 60px 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.recommended-products {
  margin-top: 40px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.recommended-products h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #303133;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.product-card {
  text-align: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-card img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 12px;
}

.product-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.product-price {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
}

@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
  }

  .cart-header,
  .cart-item {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .cart-header {
    display: none;
  }

  .cart-item {
    padding: 16px;
  }

  .item-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
