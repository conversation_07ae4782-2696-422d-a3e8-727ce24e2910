import request from '@/utils/request'
import type { ApiResponse, User } from './types'

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  phone?: string
}

export interface LoginResponse {
  user: User
  token: string
}

// 用户登录
export function login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
  return request.post('/auth/login', data)
}

// 用户注册
export function register(data: RegisterForm): Promise<ApiResponse<LoginResponse>> {
  return request.post('/auth/register', data)
}

// 获取当前用户信息
export function getCurrentUser(): Promise<ApiResponse<User>> {
  return request.get('/auth/me')
}

// 更新用户信息
export function updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
  return request.put('/auth/profile', data)
}

// 修改密码
export function changePassword(data: { currentPassword: string; newPassword: string }): Promise<ApiResponse<null>> {
  return request.put('/auth/password', data)
}
