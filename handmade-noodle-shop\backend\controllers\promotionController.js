const { Promotion, Order } = require('../models');
const { Op } = require('sequelize');
const response = require('../utils/response');

// 获取促销活动列表
const getPromotions = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status = 'active',
      type,
      current_only = false
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    const whereClause = {};
    
    if (status !== 'all') {
      whereClause.status = status;
    }
    
    if (type) {
      whereClause.type = type;
    }

    // 只获取当前有效的促销活动
    if (current_only === 'true') {
      const now = new Date();
      whereClause.start_time = { [Op.lte]: now };
      whereClause.end_time = { [Op.gte]: now };
      whereClause.status = 'active';
    }

    // 查询促销活动
    const { count, rows: promotions } = await Promotion.findAndCountAll({
      where: whereClause,
      order: [['start_time', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    response.paginated(res, promotions, {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count
    }, '获取促销活动列表成功');

  } catch (error) {
    console.error('获取促销活动列表错误:', error);
    response.serverError(res, '获取促销活动列表失败');
  }
};

// 获取单个促销活动
const getPromotionById = async (req, res) => {
  try {
    const { id } = req.params;

    const promotion = await Promotion.findByPk(id);

    if (!promotion) {
      return response.notFound(res, '促销活动不存在');
    }

    response.success(res, promotion, '获取促销活动成功');
  } catch (error) {
    console.error('获取促销活动错误:', error);
    response.serverError(res, '获取促销活动失败');
  }
};

// 创建促销活动（管理员）
const createPromotion = async (req, res) => {
  try {
    const { 
      title, 
      description, 
      type, 
      condition_amount, 
      discount_value, 
      gift_description,
      start_time,
      end_time
    } = req.body;

    // 数据验证
    const errors = [];
    
    if (!title || title.trim().length === 0) {
      errors.push('活动标题不能为空');
    }
    
    if (!type || !['discount', 'gift', 'full_reduction'].includes(type)) {
      errors.push('活动类型无效');
    }
    
    if (!start_time || !end_time) {
      errors.push('开始时间和结束时间不能为空');
    }
    
    if (start_time && end_time && new Date(start_time) >= new Date(end_time)) {
      errors.push('开始时间必须早于结束时间');
    }

    // 根据类型验证特定字段
    if (type === 'full_reduction') {
      if (!condition_amount || condition_amount <= 0) {
        errors.push('满减活动必须设置条件金额');
      }
      if (!discount_value || discount_value <= 0) {
        errors.push('满减活动必须设置减免金额');
      }
    }

    if (type === 'discount') {
      if (!discount_value || discount_value <= 0 || discount_value >= 100) {
        errors.push('折扣活动的折扣值必须在0-100之间');
      }
    }

    if (type === 'gift' && (!gift_description || gift_description.trim().length === 0)) {
      errors.push('赠品活动必须设置赠品描述');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    // 创建促销活动
    const promotion = await Promotion.create({
      title: title.trim(),
      description: description?.trim(),
      type,
      condition_amount: condition_amount ? parseFloat(condition_amount) : null,
      discount_value: discount_value ? parseFloat(discount_value) : null,
      gift_description: gift_description?.trim(),
      start_time: new Date(start_time),
      end_time: new Date(end_time)
    });

    response.success(res, promotion, '创建促销活动成功', 201);
  } catch (error) {
    console.error('创建促销活动错误:', error);
    response.serverError(res, '创建促销活动失败');
  }
};

// 更新促销活动（管理员）
const updatePromotion = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      title, 
      description, 
      type, 
      condition_amount, 
      discount_value, 
      gift_description,
      start_time,
      end_time,
      status
    } = req.body;

    const promotion = await Promotion.findByPk(id);
    if (!promotion) {
      return response.notFound(res, '促销活动不存在');
    }

    // 数据验证
    const errors = [];
    
    if (title && title.trim().length === 0) {
      errors.push('活动标题不能为空');
    }
    
    if (type && !['discount', 'gift', 'full_reduction'].includes(type)) {
      errors.push('活动类型无效');
    }
    
    if (status && !['active', 'inactive', 'expired'].includes(status)) {
      errors.push('状态值无效');
    }
    
    if (start_time && end_time && new Date(start_time) >= new Date(end_time)) {
      errors.push('开始时间必须早于结束时间');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    // 更新促销活动
    const updateData = {};
    if (title) updateData.title = title.trim();
    if (description !== undefined) updateData.description = description?.trim();
    if (type) updateData.type = type;
    if (condition_amount !== undefined) updateData.condition_amount = condition_amount ? parseFloat(condition_amount) : null;
    if (discount_value !== undefined) updateData.discount_value = discount_value ? parseFloat(discount_value) : null;
    if (gift_description !== undefined) updateData.gift_description = gift_description?.trim();
    if (start_time) updateData.start_time = new Date(start_time);
    if (end_time) updateData.end_time = new Date(end_time);
    if (status) updateData.status = status;

    await promotion.update(updateData);

    response.success(res, promotion, '更新促销活动成功');
  } catch (error) {
    console.error('更新促销活动错误:', error);
    response.serverError(res, '更新促销活动失败');
  }
};

// 删除促销活动（管理员）
const deletePromotion = async (req, res) => {
  try {
    const { id } = req.params;

    const promotion = await Promotion.findByPk(id);
    if (!promotion) {
      return response.notFound(res, '促销活动不存在');
    }

    // 检查是否有订单使用此促销活动
    const orderCount = await Order.count({
      where: { promotion_id: id }
    });

    if (orderCount > 0) {
      return response.error(res, '该促销活动已被订单使用，无法删除', 409);
    }

    await promotion.destroy();

    response.success(res, null, '删除促销活动成功');
  } catch (error) {
    console.error('删除促销活动错误:', error);
    response.serverError(res, '删除促销活动失败');
  }
};

// 检查促销活动有效性
const checkPromotionValidity = async (req, res) => {
  try {
    const { id } = req.params;
    const { order_amount } = req.query;

    const promotion = await Promotion.findByPk(id);
    if (!promotion) {
      return response.notFound(res, '促销活动不存在');
    }

    const now = new Date();
    const isTimeValid = now >= promotion.start_time && now <= promotion.end_time;
    const isStatusValid = promotion.status === 'active';
    
    let isConditionMet = true;
    let applicableDiscount = 0;

    // 检查条件是否满足
    if (promotion.type === 'full_reduction' && order_amount) {
      isConditionMet = parseFloat(order_amount) >= promotion.condition_amount;
      if (isConditionMet) {
        applicableDiscount = promotion.discount_value;
      }
    } else if (promotion.type === 'discount' && order_amount) {
      applicableDiscount = parseFloat(order_amount) * (promotion.discount_value / 100);
    }

    const isValid = isTimeValid && isStatusValid && isConditionMet;

    response.success(res, {
      promotion,
      is_valid: isValid,
      is_time_valid: isTimeValid,
      is_status_valid: isStatusValid,
      is_condition_met: isConditionMet,
      applicable_discount: applicableDiscount
    }, '促销活动有效性检查完成');

  } catch (error) {
    console.error('检查促销活动有效性错误:', error);
    response.serverError(res, '检查促销活动有效性失败');
  }
};

// 自动更新过期的促销活动状态
const updateExpiredPromotions = async () => {
  try {
    const now = new Date();
    await Promotion.update(
      { status: 'expired' },
      {
        where: {
          end_time: { [Op.lt]: now },
          status: { [Op.ne]: 'expired' }
        }
      }
    );
  } catch (error) {
    console.error('更新过期促销活动状态错误:', error);
  }
};

module.exports = {
  getPromotions,
  getPromotionById,
  createPromotion,
  updatePromotion,
  deletePromotion,
  checkPromotionValidity,
  updateExpiredPromotions
};
