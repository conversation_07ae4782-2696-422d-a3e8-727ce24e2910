<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>手工面食店管理系统</h1>
        <p>欢迎登录管理后台</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名或邮箱"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="authStore.loading"
            @click="handleLogin"
            class="login-button"
          >
            {{ authStore.loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <el-divider>测试账号</el-divider>
        <div class="test-accounts">
          <el-button 
            type="text" 
            @click="fillTestAccount('admin')"
            class="test-account-btn"
          >
            管理员: admin / admin123456
          </el-button>
          <el-button 
            type="text" 
            @click="fillTestAccount('user')"
            class="test-account-btn"
          >
            用户: testuser / user123456
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 登录表单
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 2, message: '用户名至少2个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    
    await authStore.login(loginForm)
    
    ElMessage.success('登录成功')
    
    // 根据用户角色跳转
    if (authStore.isAdmin) {
      router.push('/admin')
    } else {
      router.push('/shop')
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 填充测试账号
const fillTestAccount = (type: 'admin' | 'user') => {
  if (type === 'admin') {
    loginForm.username = 'admin'
    loginForm.password = 'admin123456'
  } else {
    loginForm.username = 'testuser'
    loginForm.password = 'user123456'
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-box {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.login-header p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  text-align: center;
}

.test-accounts {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-account-btn {
  font-size: 12px;
  color: #909399;
  padding: 4px 8px;
}

.test-account-btn:hover {
  color: #409eff;
}

:deep(.el-divider__text) {
  font-size: 12px;
  color: #c0c4cc;
}
</style>
