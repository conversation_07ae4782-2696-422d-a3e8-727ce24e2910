<template>
  <div class="products-admin">
    <div class="page-header">
      <h1>商品管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        添加商品
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入商品名称或SKU"
            clearable
            @keyup.enter="loadProducts"
          />
        </el-form-item>

        <el-form-item label="商品分类">
          <el-select
            v-model="searchForm.category_id"
            placeholder="请选择分类"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadProducts">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 商品列表 -->
    <el-card>
      <el-table
        :data="products"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column label="商品图片" width="100">
          <template #default="{ row }">
            <el-image
              v-if="row.images && row.images.length > 0"
              :src="row.images[0]"
              :alt="row.name"
              style="width: 60px; height: 60px; border-radius: 4px;"
              fit="cover"
              :preview-src-list="row.images"
            />
            <div v-else class="no-image">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="sku" label="SKU" width="120" />
        <el-table-column prop="name" label="商品名称" min-width="150" />
        <el-table-column prop="category.name" label="分类" width="100" />
        <el-table-column prop="price_per_jin" label="价格(元/斤)" width="120">
          <template #default="{ row }">
            ¥{{ row.price_per_jin }}
          </template>
        </el-table-column>
        <el-table-column label="库存" width="100">
          <template #default="{ row }">
            <span :class="{ 'low-stock': row.inventory && row.inventory.stock_jin <= row.inventory.min_stock_jin }">
              {{ row.inventory ? row.inventory.stock_jin : 0 }}斤
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ formatStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="editProduct(row)">
              编辑
            </el-button>
            <el-button
              type="text"
              :type="row.status === 'active' ? 'warning' : 'success'"
              @click="toggleStatus(row)"
            >
              {{ row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button type="text" danger @click="deleteProduct(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadProducts"
          @current-change="loadProducts"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SKU" prop="sku">
              <el-input
                v-model="form.sku"
                placeholder="请输入SKU（以g开头）"
                :disabled="!!editingId"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品分类" prop="category_id">
              <el-select
                v-model="form.category_id"
                placeholder="请选择分类"
                style="width: 100%"
              >
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="价格(元/斤)" prop="price_per_jin">
              <el-input-number
                v-model="form.price_per_jin"
                :min="0"
                :precision="2"
                placeholder="请输入价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number
                v-model="form.sort_order"
                :min="0"
                :max="999"
                placeholder="排序值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="!editingId">
            <el-form-item label="初始库存(斤)" prop="initial_stock">
              <el-input-number
                v-model="form.initial_stock"
                :min="0"
                :precision="2"
                placeholder="初始库存"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入商品描述"
          />
        </el-form-item>

        <el-form-item label="商品图片">
          <div class="image-upload-container">
            <el-upload
              :file-list="imageFileList"
              :on-change="handleImageChange"
              :on-remove="handleImageRemove"
              :before-upload="beforeImageUpload"
              :auto-upload="false"
              list-type="picture-card"
              accept="image/*"
              multiple
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
            <div class="upload-tip">
              支持jpg、png格式，单张图片不超过2MB，最多上传5张
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules, type UploadFile } from 'element-plus'
import { Plus, Search, Picture } from '@element-plus/icons-vue'
import { getProducts, createProduct, updateProduct, deleteProduct as deleteProductApi } from '@/api/products'
import { getCategories } from '@/api/categories'
import { formatStatus, formatDateTime } from '@/utils/format'
import type { Product, Category } from '@/api/types'

// 数据
const products = ref<Product[]>([])
const categories = ref<Category[]>([])
const loading = ref(false)
const dialogVisible = ref(false)
const submitting = ref(false)
const editingId = ref<number | null>(null)

// 搜索表单
const searchForm = ref({
  search: '',
  category_id: '',
  status: 'active'
})

// 分页
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

// 表单
const formRef = ref<FormInstance>()
const form = ref({
  sku: '',
  name: '',
  description: '',
  price_per_jin: 0,
  category_id: '',
  sort_order: 0,
  initial_stock: 0,
  images: [] as string[]
})

// 图片上传
const imageFileList = ref<UploadFile[]>([])

// 表单验证规则
const formRules: FormRules = {
  sku: [
    { required: true, message: '请输入SKU', trigger: 'blur' },
    { pattern: /^g/, message: 'SKU必须以g开头', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '商品名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  price_per_jin: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑商品' : '添加商品'
})

// 加载商品列表
const loadProducts = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
      ...searchForm.value
    }

    const response = await getProducts(params)
    products.value = response.data
    pagination.value.total = response.pagination.total
  } catch (error) {
    console.error('加载商品列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载分类列表
const loadCategories = async () => {
  try {
    const response = await getCategories({ status: 'active' })
    categories.value = response.data
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    search: '',
    category_id: '',
    status: 'active'
  }
  pagination.value.page = 1
  loadProducts()
}

// 显示创建对话框
const showCreateDialog = () => {
  editingId.value = null
  resetForm()
  dialogVisible.value = true
}

// 编辑商品
const editProduct = (product: Product) => {
  editingId.value = product.id
  form.value = {
    sku: product.sku,
    name: product.name,
    description: product.description || '',
    price_per_jin: product.price_per_jin,
    category_id: product.category_id,
    sort_order: product.sort_order,
    initial_stock: 0,
    images: product.images || []
  }

  // 设置图片文件列表
  imageFileList.value = (product.images || []).map((url, index) => ({
    uid: index,
    name: `image-${index}`,
    status: 'success',
    url
  }))

  dialogVisible.value = true
}

// 切换状态
const toggleStatus = async (product: Product) => {
  try {
    const newStatus = product.status === 'active' ? 'inactive' : 'active'
    await updateProduct(product.id, { status: newStatus })
    ElMessage.success('状态更新成功')
    loadProducts()
  } catch (error) {
    console.error('更新状态失败:', error)
  }
}

// 删除商品
const deleteProduct = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${product.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteProductApi(product.id)
    ElMessage.success('删除成功')
    loadProducts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除商品失败:', error)
    }
  }
}

// 图片上传处理
const handleImageChange = (file: UploadFile) => {
  if (file.raw) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const base64 = e.target?.result as string
      form.value.images.push(base64)
    }
    reader.readAsDataURL(file.raw)
  }
}

// 移除图片
const handleImageRemove = (file: UploadFile) => {
  const index = imageFileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    form.value.images.splice(index, 1)
  }
}

// 图片上传前检查
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2
  const maxImages = 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  if (form.value.images.length >= maxImages) {
    ElMessage.error(`最多只能上传 ${maxImages} 张图片!`)
    return false
  }

  return true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    const submitData = { ...form.value }
    delete submitData.initial_stock // 编辑时不需要初始库存

    if (editingId.value) {
      await updateProduct(editingId.value, submitData)
      ElMessage.success('更新成功')
    } else {
      await createProduct(submitData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadProducts()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    sku: '',
    name: '',
    description: '',
    price_per_jin: 0,
    category_id: '',
    sort_order: 0,
    initial_stock: 0,
    images: []
  }
  imageFileList.value = []
  formRef.value?.clearValidate()
}

onMounted(() => {
  loadCategories()
  loadProducts()
})
</script>

<style scoped>
.products-admin {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.no-image {
  width: 60px;
  height: 60px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 20px;
}

.low-stock {
  color: #f56c6c;
  font-weight: 600;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.image-upload-container {
  width: 100%;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-upload--picture-card) {
  width: 80px;
  height: 80px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 80px;
  height: 80px;
}
</style>
