<template>
  <div class="products-admin">
    <div class="page-header">
      <h1>商品管理</h1>
      <el-button type="primary">
        <el-icon><Plus /></el-icon>
        添加商品
      </el-button>
    </div>
    
    <el-card>
      <p>商品管理页面开发中...</p>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
</script>

<style scoped>
.products-admin {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}
</style>
