<template>
  <div class="promotions-admin">
    <div class="page-header">
      <h1>促销管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        添加促销
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="活动标题">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入活动标题"
            clearable
            @keyup.enter="loadPromotions"
          />
        </el-form-item>

        <el-form-item label="活动类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="折扣" value="discount" />
            <el-option label="赠品" value="gift" />
            <el-option label="满减" value="full_reduction" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="进行中" value="active" />
            <el-option label="已停用" value="inactive" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadPromotions">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 促销列表 -->
    <el-card>
      <el-table
        :data="promotions"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="title" label="活动标题" min-width="150" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getPromotionTypeColor(row.type)">
              {{ formatPromotionType(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="条件/优惠" width="200">
          <template #default="{ row }">
            <div v-if="row.type === 'full_reduction'">
              满¥{{ row.condition_amount }} 减¥{{ row.discount_value }}
            </div>
            <div v-else-if="row.type === 'discount'">
              {{ row.discount_value }}% 折扣
            </div>
            <div v-else-if="row.type === 'gift'">
              {{ row.gift_description }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="活动时间" width="300">
          <template #default="{ row }">
            <div>开始：{{ formatDateTime(row.start_time) }}</div>
            <div>结束：{{ formatDateTime(row.end_time) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ formatStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="editPromotion(row)">
              编辑
            </el-button>
            <el-button
              type="text"
              :type="row.status === 'active' ? 'warning' : 'success'"
              @click="toggleStatus(row)"
              v-if="row.status !== 'expired'"
            >
              {{ row.status === 'active' ? '停用' : '启用' }}
            </el-button>
            <el-button type="text" danger @click="deletePromotion(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadPromotions"
          @current-change="loadPromotions"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="活动标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入活动标题" />
        </el-form-item>

        <el-form-item label="活动类型" prop="type">
          <el-radio-group v-model="form.type" @change="handleTypeChange">
            <el-radio label="discount">折扣</el-radio>
            <el-radio label="gift">赠品</el-radio>
            <el-radio label="full_reduction">满减</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="form.type === 'full_reduction'"
          label="满减条件(元)"
          prop="condition_amount"
        >
          <el-input-number
            v-model="form.condition_amount"
            :min="0"
            :precision="2"
            placeholder="满多少元"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item
          v-if="form.type === 'full_reduction'"
          label="减免金额(元)"
          prop="discount_value"
        >
          <el-input-number
            v-model="form.discount_value"
            :min="0"
            :precision="2"
            placeholder="减免多少元"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item
          v-if="form.type === 'discount'"
          label="折扣百分比"
          prop="discount_value"
        >
          <el-input-number
            v-model="form.discount_value"
            :min="1"
            :max="99"
            placeholder="折扣百分比"
            style="width: 100%"
          />
          <span class="form-tip">例如：输入10表示9折</span>
        </el-form-item>

        <el-form-item
          v-if="form.type === 'gift'"
          label="赠品描述"
          prop="gift_description"
        >
          <el-input
            v-model="form.gift_description"
            placeholder="请输入赠品描述"
          />
        </el-form-item>

        <el-form-item label="活动时间" prop="time_range">
          <el-date-picker
            v-model="form.time_range"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="活动描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入活动描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { formatPromotionType, formatStatus, formatDateTime } from '@/utils/format'

// 促销类型定义
interface Promotion {
  id: number
  title: string
  description?: string
  type: 'discount' | 'gift' | 'full_reduction'
  condition_amount?: number
  discount_value?: number
  gift_description?: string
  start_time: string
  end_time: string
  status: 'active' | 'inactive' | 'expired'
  createdAt: string
  updatedAt: string
}

// 数据
const promotions = ref<Promotion[]>([])
const loading = ref(false)
const dialogVisible = ref(false)
const submitting = ref(false)
const editingId = ref<number | null>(null)

// 搜索表单
const searchForm = ref({
  search: '',
  type: '',
  status: 'active'
})

// 分页
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

// 表单
const formRef = ref<FormInstance>()
const form = ref({
  title: '',
  description: '',
  type: 'discount' as 'discount' | 'gift' | 'full_reduction',
  condition_amount: 0,
  discount_value: 0,
  gift_description: '',
  time_range: [] as string[]
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入活动标题', trigger: 'blur' },
    { min: 2, max: 100, message: '活动标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择活动类型', trigger: 'change' }
  ],
  time_range: [
    { required: true, message: '请选择活动时间', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑促销活动' : '添加促销活动'
})

// 获取促销类型颜色
const getPromotionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    discount: 'success',
    gift: 'warning',
    full_reduction: 'danger'
  }
  return colorMap[type] || 'info'
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    expired: 'info'
  }
  return colorMap[status] || 'info'
}

// 加载促销列表
const loadPromotions = async () => {
  loading.value = true
  try {
    // 模拟数据，实际应该调用API
    const mockData = [
      {
        id: 1,
        title: '消费满50元送酱油醋',
        description: '单笔消费满50元即可获赠精装酱油醋一套',
        type: 'gift' as const,
        condition_amount: 50,
        gift_description: '精装酱油醋一套',
        start_time: new Date().toISOString(),
        end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
    promotions.value = mockData
    pagination.value.total = mockData.length
  } catch (error) {
    console.error('加载促销列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    search: '',
    type: '',
    status: 'active'
  }
  pagination.value.page = 1
  loadPromotions()
}

// 显示创建对话框
const showCreateDialog = () => {
  editingId.value = null
  resetForm()
  dialogVisible.value = true
}

// 编辑促销
const editPromotion = (promotion: Promotion) => {
  editingId.value = promotion.id
  form.value = {
    title: promotion.title,
    description: promotion.description || '',
    type: promotion.type,
    condition_amount: promotion.condition_amount || 0,
    discount_value: promotion.discount_value || 0,
    gift_description: promotion.gift_description || '',
    time_range: [promotion.start_time, promotion.end_time]
  }
  dialogVisible.value = true
}

// 切换状态
const toggleStatus = async (promotion: Promotion) => {
  try {
    const newStatus = promotion.status === 'active' ? 'inactive' : 'active'
    // 这里应该调用API更新状态
    ElMessage.success('状态更新成功')
    loadPromotions()
  } catch (error) {
    console.error('更新状态失败:', error)
  }
}

// 删除促销
const deletePromotion = async (promotion: Promotion) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除促销活动"${promotion.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该调用API删除
    ElMessage.success('删除成功')
    loadPromotions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除促销失败:', error)
    }
  }
}

// 处理类型变化
const handleTypeChange = () => {
  // 重置相关字段
  form.value.condition_amount = 0
  form.value.discount_value = 0
  form.value.gift_description = ''
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    // 这里应该调用API提交数据
    ElMessage.success(editingId.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    loadPromotions()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    title: '',
    description: '',
    type: 'discount',
    condition_amount: 0,
    discount_value: 0,
    gift_description: '',
    time_range: []
  }
  formRef.value?.clearValidate()
}

onMounted(() => {
  loadPromotions()
})
</script>

<style scoped>
.promotions-admin {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}
</style></script>
