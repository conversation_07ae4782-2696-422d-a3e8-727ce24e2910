<template>
  <div class="products-page">
    <div class="page-container">
      <!-- 页面标题和筛选 -->
      <div class="page-header">
        <h1>商品列表</h1>
        <div class="filters">
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            clearable
            @change="handleCategoryChange"
          >
            <el-option label="全部分类" value="" />
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>

          <el-input
            v-model="searchKeyword"
            placeholder="搜索商品"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px; margin-left: 12px;"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 商品网格 -->
      <div class="products-grid" v-loading="loading">
        <div
          v-for="product in products"
          :key="product.id"
          class="product-card"
          @click="goToProductDetail(product.id)"
        >
          <div class="product-image">
            <img
              :src="product.images?.[0] || '/placeholder.jpg'"
              :alt="product.name"
            />
            <div class="product-overlay">
              <el-button type="primary" round>查看详情</el-button>
            </div>
          </div>

          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-desc">{{ product.description }}</p>
            <div class="product-meta">
              <span class="product-category">{{ product.category?.name }}</span>
              <span class="product-sku">SKU: {{ product.sku }}</span>
            </div>
            <div class="product-price">
              <span class="price">¥{{ product.price_per_jin }}</span>
              <span class="unit">/斤</span>
            </div>
            <div class="product-actions">
              <el-button
                type="primary"
                @click.stop="addToCart(product)"
                :disabled="!product.inventory || product.inventory.stock_jin <= 0"
              >
                <el-icon><ShoppingCart /></el-icon>
                {{ product.inventory && product.inventory.stock_jin > 0 ? '加入购物车' : '暂无库存' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && products.length === 0" class="empty-state">
        <el-empty description="暂无商品" />
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="products.length > 0">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[12, 24, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadProducts"
          @current-change="loadProducts"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, ShoppingCart } from '@element-plus/icons-vue'
import { getProducts } from '@/api/products'
import { getCategories } from '@/api/categories'
import { useCartStore } from '@/stores/cart'
import type { Product, Category } from '@/api/types'

const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()

// 数据
const products = ref<Product[]>([])
const categories = ref<Category[]>([])
const loading = ref(false)
const selectedCategory = ref('')
const searchKeyword = ref('')

// 分页
const pagination = ref({
  page: 1,
  limit: 12,
  total: 0
})

// 加载商品列表
const loadProducts = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
      status: 'active',
      category_id: selectedCategory.value,
      search: searchKeyword.value
    }

    const response = await getProducts(params)
    products.value = response.data
    pagination.value.total = response.pagination.total
  } catch (error) {
    console.error('加载商品列表失败:', error)
    ElMessage.error('加载商品失败')
  } finally {
    loading.value = false
  }
}

// 加载分类列表
const loadCategories = async () => {
  try {
    const response = await getCategories({ status: 'active' })
    categories.value = response.data
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

// 处理分类变化
const handleCategoryChange = () => {
  pagination.value.page = 1
  loadProducts()
}

// 处理搜索
const handleSearch = () => {
  pagination.value.page = 1
  loadProducts()
}

// 跳转到商品详情
const goToProductDetail = (productId: number) => {
  router.push(`/shop/products/${productId}`)
}

// 添加到购物车
const addToCart = (product: Product) => {
  cartStore.addItem(product, 1) // 默认添加1斤
  ElMessage.success(`${product.name} 已添加到购物车`)
}

// 监听路由参数变化
watch(() => route.query.category, (newCategory) => {
  if (newCategory) {
    selectedCategory.value = newCategory as string
    loadProducts()
  }
}, { immediate: true })

onMounted(() => {
  loadCategories()
  loadProducts()
})
</script>

<style scoped>
.products-page {
  min-height: calc(100vh - 200px);
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  margin: 0;
  font-size: 28px;
  color: #303133;
}

.filters {
  display: flex;
  align-items: center;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.product-info {
  padding: 20px;
}

.product-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 14px;
  color: #909399;
  margin: 0 0 12px 0;
  height: 40px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 12px;
  color: #c0c4cc;
}

.product-price {
  margin-bottom: 16px;
}

.price {
  font-size: 24px;
  font-weight: 600;
  color: #f56c6c;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.product-actions {
  text-align: center;
}

.product-actions .el-button {
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination-wrapper {
  text-align: center;
  padding: 20px 0;
}
</style>
