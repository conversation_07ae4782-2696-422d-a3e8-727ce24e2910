<template>
  <div class="shop-home">
    <!-- 轮播图 -->
    <el-carousel height="300px" class="banner">
      <el-carousel-item>
        <div class="banner-item">
          <h2>消费满50元送酱油醋</h2>
          <p>精装酱油醋一套，数量有限，先到先得</p>
        </div>
      </el-carousel-item>
      <el-carousel-item>
        <div class="banner-item">
          <h2>新鲜手工面条</h2>
          <p>每日现做，保证新鲜，口感劲道</p>
        </div>
      </el-carousel-item>
    </el-carousel>
    
    <!-- 商品分类 -->
    <div class="categories-section">
      <h2>商品分类</h2>
      <el-row :gutter="20">
        <el-col :span="6" v-for="category in categories" :key="category.id">
          <el-card 
            class="category-card" 
            @click="$router.push(`/shop/products?category=${category.id}`)"
          >
            <div class="category-content">
              <div class="category-icon">
                <el-icon><Goods /></el-icon>
              </div>
              <h3>{{ category.name }}</h3>
              <p>{{ category.description }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 热门商品 -->
    <div class="products-section">
      <h2>热门商品</h2>
      <el-row :gutter="20">
        <el-col :span="6" v-for="product in hotProducts" :key="product.id">
          <el-card class="product-card">
            <div class="product-image">
              <img 
                :src="product.images?.[0] || '/placeholder.jpg'" 
                :alt="product.name"
              >
            </div>
            <div class="product-info">
              <h3>{{ product.name }}</h3>
              <p class="product-desc">{{ product.description }}</p>
              <div class="product-price">
                <span class="price">¥{{ product.price_per_jin }}/斤</span>
              </div>
              <el-button 
                type="primary" 
                @click="$router.push(`/shop/products/${product.id}`)"
                class="view-btn"
              >
                查看详情
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { Goods } from '@element-plus/icons-vue'
import { getCategories } from '@/api/categories'
import { getProducts } from '@/api/products'
import type { Category, Product } from '@/api/types'

// 数据
const categories = ref<Category[]>([])
const hotProducts = ref<Product[]>([])

// 加载分类
const loadCategories = async () => {
  try {
    const response = await getCategories({ status: 'active' })
    categories.value = response.data
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 加载热门商品
const loadHotProducts = async () => {
  try {
    const response = await getProducts({ 
      status: 'active', 
      limit: 8,
      sort_by: 'createdAt',
      sort_order: 'DESC'
    })
    hotProducts.value = response.data
  } catch (error) {
    console.error('加载商品失败:', error)
  }
}

onMounted(() => {
  loadCategories()
  loadHotProducts()
})
</script>

<style scoped>
.shop-home {
  max-width: 1200px;
  margin: 0 auto;
}

.banner {
  margin-bottom: 40px;
  border-radius: 8px;
  overflow: hidden;
}

.banner-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.banner-item h2 {
  font-size: 32px;
  margin: 0 0 10px 0;
}

.banner-item p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.categories-section,
.products-section {
  margin-bottom: 40px;
}

.categories-section h2,
.products-section h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 20px;
  text-align: center;
}

.category-card {
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-content {
  text-align: center;
  padding: 20px;
}

.category-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 15px;
}

.category-content h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 10px 0;
}

.category-content p {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.product-card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
  height: 200px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 15px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info h3 {
  font-size: 16px;
  color: #303133;
  margin: 0 0 8px 0;
}

.product-desc {
  font-size: 14px;
  color: #909399;
  margin: 0 0 15px 0;
  height: 40px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  margin-bottom: 15px;
}

.price {
  font-size: 18px;
  color: #f56c6c;
  font-weight: 600;
}

.view-btn {
  width: 100%;
}
</style>
