<template>
  <div class="checkout-page">
    <div class="page-container">
      <div class="page-header">
        <h1>确认订单</h1>
        <div class="steps">
          <el-steps :active="currentStep" finish-status="success">
            <el-step title="确认商品" />
            <el-step title="填写信息" />
            <el-step title="支付订单" />
          </el-steps>
        </div>
      </div>
      
      <div class="checkout-content">
        <!-- 商品确认 -->
        <div class="checkout-section">
          <h2>商品清单</h2>
          <div class="order-items">
            <div
              v-for="item in cartStore.items"
              :key="item.id"
              class="order-item"
            >
              <div class="item-image">
                <img
                  :src="item.product.images?.[0] || '/placeholder.jpg'"
                  :alt="item.product.name"
                />
              </div>
              <div class="item-info">
                <h4>{{ item.product.name }}</h4>
                <p>SKU: {{ item.product.sku }}</p>
              </div>
              <div class="item-price">
                ¥{{ item.unit_price }}/斤
              </div>
              <div class="item-quantity">
                {{ item.quantity }}斤
              </div>
              <div class="item-subtotal">
                ¥{{ item.subtotal.toFixed(2) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 订单信息表单 -->
        <div class="checkout-section">
          <h2>订单信息</h2>
          <el-form
            ref="orderFormRef"
            :model="orderForm"
            :rules="orderFormRules"
            label-width="100px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contact_name">
                  <el-input
                    v-model="orderForm.contact_name"
                    placeholder="请输入联系人姓名"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contact_phone">
                  <el-input
                    v-model="orderForm.contact_phone"
                    placeholder="请输入联系电话"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="取货时间" prop="pickup_time">
              <el-date-picker
                v-model="orderForm.pickup_time"
                type="datetime"
                placeholder="请选择取货时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                :disabled-hours="disabledHours"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item label="订单备注">
              <el-input
                v-model="orderForm.notes"
                type="textarea"
                :rows="3"
                placeholder="请输入订单备注（选填）"
              />
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 订单汇总 -->
        <div class="checkout-section">
          <h2>费用明细</h2>
          <div class="order-summary">
            <div class="summary-row">
              <span>商品总额：</span>
              <span>¥{{ cartStore.totalAmount.toFixed(2) }}</span>
            </div>
            
            <div class="summary-row" v-if="promotionDiscount > 0">
              <span>促销优惠：</span>
              <span class="discount">-¥{{ promotionDiscount.toFixed(2) }}</span>
            </div>
            
            <div class="summary-row total">
              <span>应付总额：</span>
              <span class="total-amount">¥{{ finalAmount.toFixed(2) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 提交按钮 -->
        <div class="checkout-actions">
          <el-button size="large" @click="$router.back()">
            返回购物车
          </el-button>
          <el-button
            type="primary"
            size="large"
            @click="submitOrder"
            :loading="submitting"
          >
            提交订单
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useCartStore } from '@/stores/cart'
import { useAuthStore } from '@/stores/auth'
import { createOrder } from '@/api/orders'

const router = useRouter()
const cartStore = useCartStore()
const authStore = useAuthStore()

// 数据
const currentStep = ref(1)
const submitting = ref(false)
const promotionDiscount = ref(0)

// 表单
const orderFormRef = ref<FormInstance>()
const orderForm = ref({
  contact_name: '',
  contact_phone: '',
  pickup_time: '',
  notes: ''
})

// 表单验证规则
const orderFormRules: FormRules = {
  contact_name: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contact_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  pickup_time: [
    { required: true, message: '请选择取货时间', trigger: 'change' }
  ]
}

// 计算属性
const finalAmount = computed(() => {
  return Math.max(0, cartStore.totalAmount - promotionDiscount.value)
})

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
}

// 禁用营业时间外的小时
const disabledHours = () => {
  const hours = []
  for (let i = 0; i < 24; i++) {
    if (i < 8 || i > 20) { // 营业时间 8:00-20:00
      hours.push(i)
    }
  }
  return hours
}

// 检查促销活动
const checkPromotions = () => {
  const totalAmount = cartStore.totalAmount
  
  if (totalAmount >= 50) {
    promotionDiscount.value = 5
  } else {
    promotionDiscount.value = 0
  }
}

// 提交订单
const submitOrder = async () => {
  if (!orderFormRef.value) return
  
  try {
    await orderFormRef.value.validate()
    
    if (cartStore.isEmpty) {
      ElMessage.warning('购物车是空的')
      return
    }
    
    submitting.value = true
    
    // 构建订单数据
    const orderData = {
      items: cartStore.items.map(item => ({
        product_id: item.product.id,
        quantity_jin: item.quantity,
        unit_price: item.unit_price
      })),
      total_amount: cartStore.totalAmount,
      discount_amount: promotionDiscount.value,
      final_amount: finalAmount.value,
      pickup_time: orderForm.value.pickup_time,
      notes: orderForm.value.notes,
      contact_info: {
        name: orderForm.value.contact_name,
        phone: orderForm.value.contact_phone
      }
    }
    
    const response = await createOrder(orderData)
    
    // 清空购物车
    cartStore.clearCart()
    
    ElMessage.success('订单提交成功')
    
    // 跳转到订单详情页面
    router.push(`/shop/orders/${response.data.id}`)
    
  } catch (error) {
    console.error('提交订单失败:', error)
    ElMessage.error('提交订单失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 初始化表单数据
const initFormData = () => {
  if (authStore.user) {
    orderForm.value.contact_name = authStore.user.username || ''
    orderForm.value.contact_phone = authStore.user.phone || ''
  }
}

onMounted(() => {
  // 检查是否有商品
  if (cartStore.isEmpty) {
    ElMessage.warning('购物车是空的')
    router.push('/shop/cart')
    return
  }
  
  // 检查是否登录
  if (!authStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  initFormData()
  checkPromotions()
})
</script>

<style scoped>
.checkout-page {
  min-height: calc(100vh - 200px);
  background-color: #f5f7fa;
}

.page-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #303133;
}

.checkout-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.checkout-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.checkout-section h2 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-item {
  display: grid;
  grid-template-columns: 60px 2fr 1fr 1fr 1fr;
  gap: 16px;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.item-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.item-price,
.item-quantity,
.item-subtotal {
  text-align: center;
  font-size: 14px;
  color: #606266;
}

.item-subtotal {
  font-weight: 600;
  color: #303133;
}

.order-summary {
  max-width: 400px;
  margin-left: auto;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 16px;
}

.summary-row.total {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-top: 1px solid #e4e7ed;
  padding-top: 12px;
  margin-top: 12px;
}

.discount {
  color: #f56c6c;
}

.total-amount {
  color: #f56c6c;
  font-size: 20px;
}

.checkout-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
}

.checkout-actions .el-button {
  min-width: 120px;
  height: 44px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .order-item {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 8px;
  }
  
  .checkout-actions {
    flex-direction: column;
  }
  
  .checkout-actions .el-button {
    width: 100%;
  }
}
</style>
