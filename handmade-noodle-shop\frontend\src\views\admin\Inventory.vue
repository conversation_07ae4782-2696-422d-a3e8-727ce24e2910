<template>
  <div class="inventory-admin">
    <div class="page-header">
      <h1>库存管理</h1>
      <div class="header-actions">
        <el-button type="warning" @click="showLowStockDialog">
          <el-icon><Warning /></el-icon>
          低库存商品
        </el-button>
        <el-button type="primary" @click="showBatchUpdateDialog">
          <el-icon><Edit /></el-icon>
          批量更新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入商品名称或SKU"
            clearable
            @keyup.enter="loadInventory"
          />
        </el-form-item>

        <el-form-item label="商品分类">
          <el-select
            v-model="searchForm.category_id"
            placeholder="请选择分类"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="库存状态">
          <el-select
            v-model="searchForm.low_stock"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="正常库存" value="false" />
            <el-option label="低库存" value="true" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadInventory">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 库存列表 -->
    <el-card>
      <el-table
        :data="inventory"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="商品图片" width="100">
          <template #default="{ row }">
            <el-image
              v-if="row.product.images && row.product.images.length > 0"
              :src="row.product.images[0]"
              :alt="row.product.name"
              style="width: 60px; height: 60px; border-radius: 4px;"
              fit="cover"
            />
            <div v-else class="no-image">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="product.sku" label="SKU" width="120" />
        <el-table-column prop="product.name" label="商品名称" min-width="150" />
        <el-table-column prop="product.category.name" label="分类" width="100" />
        <el-table-column prop="product.price_per_jin" label="价格(元/斤)" width="120">
          <template #default="{ row }">
            ¥{{ row.product.price_per_jin }}
          </template>
        </el-table-column>
        <el-table-column label="当前库存" width="120">
          <template #default="{ row }">
            <span :class="{ 'low-stock': row.stock_jin <= row.min_stock_jin }">
              {{ row.stock_jin }}斤
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="min_stock_jin" label="最低库存" width="120">
          <template #default="{ row }">
            {{ row.min_stock_jin }}斤
          </template>
        </el-table-column>
        <el-table-column prop="last_updated" label="最后更新" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.last_updated) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="editInventory(row)">
              编辑库存
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadInventory"
          @current-change="loadInventory"
        />
      </div>
    </el-card>

    <!-- 编辑库存对话框 -->
    <el-dialog
      title="编辑库存"
      v-model="editDialogVisible"
      width="500px"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="120px"
      >
        <el-form-item label="商品信息">
          <div class="product-info">
            <strong>{{ editForm.product?.name }}</strong>
            <span class="sku">SKU: {{ editForm.product?.sku }}</span>
          </div>
        </el-form-item>

        <el-form-item label="操作类型" prop="operation_type">
          <el-radio-group v-model="editForm.operation_type">
            <el-radio label="set">设置库存</el-radio>
            <el-radio label="add">增加库存</el-radio>
            <el-radio label="subtract">减少库存</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          :label="editForm.operation_type === 'set' ? '设置库存(斤)' : '变更数量(斤)'"
          prop="change_amount"
        >
          <el-input-number
            v-model="editForm.change_amount"
            :min="0"
            :precision="2"
            placeholder="请输入数量"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="最低库存(斤)" prop="min_stock_jin">
          <el-input-number
            v-model="editForm.min_stock_jin"
            :min="0"
            :precision="2"
            placeholder="请输入最低库存"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="editForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEditForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 低库存商品对话框 -->
    <el-dialog
      title="低库存商品"
      v-model="lowStockDialogVisible"
      width="800px"
    >
      <el-table :data="lowStockProducts" v-loading="lowStockLoading">
        <el-table-column prop="product.name" label="商品名称" />
        <el-table-column prop="product.sku" label="SKU" width="120" />
        <el-table-column label="当前库存" width="100">
          <template #default="{ row }">
            <span class="low-stock">{{ row.stock_jin }}斤</span>
          </template>
        </el-table-column>
        <el-table-column prop="min_stock_jin" label="最低库存" width="100">
          <template #default="{ row }">
            {{ row.min_stock_jin }}斤
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="text" @click="editInventoryFromLowStock(row)">
              补充库存
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Warning, Edit, Search, Picture } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format'
// 这里需要创建库存相关的API
// import { getInventory, updateInventory, getLowStockProducts } from '@/api/inventory'
import { getCategories } from '@/api/categories'
import type { Category } from '@/api/types'

// 库存类型定义
interface InventoryItem {
  id: number
  product_id: number
  stock_jin: number
  min_stock_jin: number
  last_updated: string
  product: {
    id: number
    sku: string
    name: string
    price_per_jin: number
    images?: string[]
    category: {
      id: number
      name: string
    }
  }
}

// 数据
const inventory = ref<InventoryItem[]>([])
const categories = ref<Category[]>([])
const lowStockProducts = ref<InventoryItem[]>([])
const selectedItems = ref<InventoryItem[]>([])
const loading = ref(false)
const lowStockLoading = ref(false)
const editDialogVisible = ref(false)
const lowStockDialogVisible = ref(false)
const submitting = ref(false)

// 搜索表单
const searchForm = ref({
  search: '',
  category_id: '',
  low_stock: ''
})

// 分页
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

// 编辑表单
const editFormRef = ref<FormInstance>()
const editForm = ref({
  product_id: 0,
  product: null as any,
  operation_type: 'set',
  change_amount: 0,
  min_stock_jin: 0,
  notes: ''
})

// 编辑表单验证规则
const editFormRules: FormRules = {
  operation_type: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  change_amount: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '数量不能为负数', trigger: 'blur' }
  ]
}

// 加载库存列表
const loadInventory = async () => {
  loading.value = true
  try {
    // 模拟数据，实际应该调用API
    const mockData = [
      {
        id: 1,
        product_id: 1,
        stock_jin: 5.5,
        min_stock_jin: 10,
        last_updated: new Date().toISOString(),
        product: {
          id: 1,
          sku: 'g001',
          name: '手工拉面',
          price_per_jin: 12.00,
          images: [],
          category: { id: 1, name: '手工面条' }
        }
      }
    ]
    inventory.value = mockData
    pagination.value.total = mockData.length
  } catch (error) {
    console.error('加载库存列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载分类列表
const loadCategories = async () => {
  try {
    const response = await getCategories({ status: 'active' })
    categories.value = response.data
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    search: '',
    category_id: '',
    low_stock: ''
  }
  pagination.value.page = 1
  loadInventory()
}

// 处理选择变化
const handleSelectionChange = (selection: InventoryItem[]) => {
  selectedItems.value = selection
}

// 编辑库存
const editInventory = (item: InventoryItem) => {
  editForm.value = {
    product_id: item.product_id,
    product: item.product,
    operation_type: 'set',
    change_amount: item.stock_jin,
    min_stock_jin: item.min_stock_jin,
    notes: ''
  }
  editDialogVisible.value = true
}

// 从低库存列表编辑
const editInventoryFromLowStock = (item: InventoryItem) => {
  lowStockDialogVisible.value = false
  editInventory(item)
}

// 显示低库存对话框
const showLowStockDialog = async () => {
  lowStockLoading.value = true
  lowStockDialogVisible.value = true

  try {
    // 模拟低库存数据
    lowStockProducts.value = inventory.value.filter(item =>
      item.stock_jin <= item.min_stock_jin
    )
  } catch (error) {
    console.error('加载低库存商品失败:', error)
  } finally {
    lowStockLoading.value = false
  }
}

// 显示批量更新对话框
const showBatchUpdateDialog = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择要更新的商品')
    return
  }
  ElMessage.info('批量更新功能开发中...')
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()

    submitting.value = true

    // 这里应该调用更新库存的API
    ElMessage.success('库存更新成功')
    editDialogVisible.value = false
    loadInventory()
  } catch (error) {
    console.error('更新库存失败:', error)
  } finally {
    submitting.value = false
  }
}

// 重置编辑表单
const resetEditForm = () => {
  editForm.value = {
    product_id: 0,
    product: null,
    operation_type: 'set',
    change_amount: 0,
    min_stock_jin: 0,
    notes: ''
  }
  editFormRef.value?.clearValidate()
}

onMounted(() => {
  loadCategories()
  loadInventory()
})
</script>

<style scoped>
.inventory-admin {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 20px;
}

.no-image {
  width: 60px;
  height: 60px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 20px;
}

.low-stock {
  color: #f56c6c;
  font-weight: 600;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sku {
  font-size: 12px;
  color: #909399;
}
</style></script>
