const { User } = require('../models');
const { Op } = require('sequelize');
const {
  hashPassword,
  comparePassword,
  generateToken,
  validateEmail,
  validatePhone,
  validatePassword
} = require('../utils/auth');
const response = require('../utils/response');

// 用户注册
const register = async (req, res) => {
  try {
    const { username, email, password, phone } = req.body;

    // 数据验证
    const errors = [];
    
    if (!username || username.length < 2) {
      errors.push('用户名至少2个字符');
    }
    
    if (!email || !validateEmail(email)) {
      errors.push('请输入有效的邮箱地址');
    }
    
    if (!password || !validatePassword(password)) {
      errors.push('密码至少8位，包含字母和数字');
    }
    
    if (phone && !validatePhone(phone)) {
      errors.push('请输入有效的手机号');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    // 检查用户名是否已存在
    const existingUsername = await User.findOne({ where: { username } });
    if (existingUsername) {
      return response.error(res, '用户名已存在', 409);
    }

    // 检查邮箱是否已存在
    const existingEmail = await User.findOne({ where: { email } });
    if (existingEmail) {
      return response.error(res, '邮箱已被注册', 409);
    }

    // 检查手机号是否已存在
    if (phone) {
      const existingPhone = await User.findOne({ where: { phone } });
      if (existingPhone) {
        return response.error(res, '手机号已被注册', 409);
      }
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const user = await User.create({
      username,
      email,
      password: hashedPassword,
      phone,
      role: 'user'
    });

    // 生成JWT令牌
    const token = generateToken(user.id, user.role);

    // 返回用户信息（不包含密码）
    const userInfo = {
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      createdAt: user.createdAt
    };

    response.success(res, {
      user: userInfo,
      token
    }, '注册成功', 201);

  } catch (error) {
    console.error('注册错误:', error);
    response.serverError(res, '注册失败');
  }
};

// 用户登录
const login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 数据验证
    if (!username || !password) {
      return response.validationError(res, ['用户名和密码不能为空']);
    }

    // 查找用户（支持用户名或邮箱登录）
    const user = await User.findOne({
      where: {
        [Op.or]: [
          { username },
          { email: username }
        ]
      }
    });

    if (!user) {
      return response.error(res, '用户名或密码错误', 401);
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return response.error(res, '账户已被禁用', 401);
    }

    // 验证密码
    const isPasswordValid = await comparePassword(password, user.password);
    if (!isPasswordValid) {
      return response.error(res, '用户名或密码错误', 401);
    }

    // 生成JWT令牌
    const token = generateToken(user.id, user.role);

    // 返回用户信息（不包含密码）
    const userInfo = {
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      createdAt: user.createdAt
    };

    response.success(res, {
      user: userInfo,
      token
    }, '登录成功');

  } catch (error) {
    console.error('登录错误:', error);
    response.serverError(res, '登录失败');
  }
};

// 获取当前用户信息
const getCurrentUser = async (req, res) => {
  try {
    const userInfo = {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
      phone: req.user.phone,
      role: req.user.role,
      status: req.user.status,
      avatar: req.user.avatar,
      createdAt: req.user.createdAt,
      updatedAt: req.user.updatedAt
    };

    response.success(res, userInfo, '获取用户信息成功');
  } catch (error) {
    console.error('获取用户信息错误:', error);
    response.serverError(res, '获取用户信息失败');
  }
};

// 更新用户信息
const updateProfile = async (req, res) => {
  try {
    const { username, email, phone, avatar } = req.body;
    const userId = req.user.id;

    // 数据验证
    const errors = [];
    
    if (username && username.length < 2) {
      errors.push('用户名至少2个字符');
    }
    
    if (email && !validateEmail(email)) {
      errors.push('请输入有效的邮箱地址');
    }
    
    if (phone && !validatePhone(phone)) {
      errors.push('请输入有效的手机号');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    // 检查用户名是否已被其他用户使用
    if (username) {
      const existingUsername = await User.findOne({
        where: {
          username,
          id: { [Op.ne]: userId }
        }
      });
      if (existingUsername) {
        return response.error(res, '用户名已存在', 409);
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (email) {
      const existingEmail = await User.findOne({
        where: {
          email,
          id: { [Op.ne]: userId }
        }
      });
      if (existingEmail) {
        return response.error(res, '邮箱已被使用', 409);
      }
    }

    // 检查手机号是否已被其他用户使用
    if (phone) {
      const existingPhone = await User.findOne({
        where: {
          phone,
          id: { [Op.ne]: userId }
        }
      });
      if (existingPhone) {
        return response.error(res, '手机号已被使用', 409);
      }
    }

    // 更新用户信息
    const updateData = {};
    if (username) updateData.username = username;
    if (email) updateData.email = email;
    if (phone) updateData.phone = phone;
    if (avatar) updateData.avatar = avatar;

    await User.update(updateData, {
      where: { id: userId }
    });

    // 获取更新后的用户信息
    const updatedUser = await User.findByPk(userId, {
      attributes: { exclude: ['password'] }
    });

    response.success(res, updatedUser, '更新成功');

  } catch (error) {
    console.error('更新用户信息错误:', error);
    response.serverError(res, '更新失败');
  }
};

// 修改密码
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // 数据验证
    if (!currentPassword || !newPassword) {
      return response.validationError(res, ['当前密码和新密码不能为空']);
    }

    if (!validatePassword(newPassword)) {
      return response.validationError(res, ['新密码至少8位，包含字母和数字']);
    }

    // 获取用户当前密码
    const user = await User.findByPk(userId);
    if (!user) {
      return response.notFound(res, '用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await comparePassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return response.error(res, '当前密码错误', 401);
    }

    // 加密新密码
    const hashedNewPassword = await hashPassword(newPassword);

    // 更新密码
    await User.update(
      { password: hashedNewPassword },
      { where: { id: userId } }
    );

    response.success(res, null, '密码修改成功');

  } catch (error) {
    console.error('修改密码错误:', error);
    response.serverError(res, '密码修改失败');
  }
};

module.exports = {
  register,
  login,
  getCurrentUser,
  updateProfile,
  changePassword
};
