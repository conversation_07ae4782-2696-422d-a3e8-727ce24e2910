# 手工面食店电商网站开发任务计划

## 项目概述
为手工面食店开发一个完整的电商网站系统，包含前台展示和后台管理功能。

## 技术栈
- 前端：Vue 3 + Vite + Element Plus + Pinia + Vue Router
- 后端：Node.js + Express + Sequelize ORM
- 数据库：MySQL 8（远程数据库：cat.201626.xyz:3306）
- 部署：前端Vercel，后端云服务器

## 开发阶段

### 第一阶段：项目初始化和数据库设计 ✅
- [x] 创建Vue 3前端项目（使用Vite + Element Plus）
- [x] 创建Node.js后端项目（Express + Sequelize）
- [x] 设计MySQL数据库结构（8个核心表）
- [x] 配置开发环境
- [x] 测试前后端基础运行

### 第二阶段：后端API开发 ✅
- [x] 用户认证系统（JWT + 角色权限）
- [x] 商品管理API（CRUD + 图片Base64存储）
- [x] 库存管理API（按斤计算逻辑）
- [x] 分类管理API（CRUD操作）
- [x] 促销活动API（时间控制逻辑）
- [x] 订单管理API（预订流程）
- [x] 天气API集成（OpenWeatherMap）

### 第三阶段：后台管理界面开发 📋
- [ ] 管理员登录页面
- [ ] 商品管理界面（支持拖拽上传图片）
- [ ] 库存管理界面
- [ ] 促销活动管理界面
- [ ] 订单管理界面
- [ ] 数据统计看板

### 第四阶段：前台用户界面开发 📋
- [ ] 首页设计（促销横幅 + 商品展示）
- [ ] 商品分类浏览页面
- [ ] 商品详情页面（多图展示）
- [ ] 用户注册登录页面
- [ ] 预订下单页面
- [ ] 用户评价系统
- [ ] 天气显示组件（右上角）

### 第五阶段：功能联调和部署 📋
- [ ] 前后端接口联调
- [ ] 功能测试和优化
- [ ] SEO优化配置
- [ ] Vercel前端部署
- [ ] 云服务器后端部署

## 特殊要求
- 图片存储：Base64格式存储在数据库中
- 天气API：使用免费的OpenWeatherMap API
- 弹窗消息：一律居中显示
- 不需要支付功能
- 不需要通知功能

## 当前状态
- 项目初始化完成 ✅
- 数据库连接成功 ✅
- 前后端服务正常运行 ✅
- 后端API开发基本完成 ✅
- 用户认证系统完成 ✅
- 商品和分类管理API完成 ✅
- 库存管理API完成 ✅
- 初始数据和测试用户创建完成 ✅

## 已完成的API接口
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- GET /api/auth/me - 获取当前用户信息
- PUT /api/auth/profile - 更新用户信息
- PUT /api/auth/password - 修改密码
- GET /api/categories - 获取分类列表
- GET /api/categories/:id - 获取单个分类
- POST /api/categories - 创建分类（管理员）
- PUT /api/categories/:id - 更新分类（管理员）
- DELETE /api/categories/:id - 删除分类（管理员）
- GET /api/products - 获取商品列表
- GET /api/products/:id - 获取单个商品
- POST /api/products - 创建商品（管理员）
- PUT /api/products/:id - 更新商品（管理员）
- DELETE /api/products/:id - 删除商品（管理员）
- GET /api/inventory - 获取库存列表（管理员）
- GET /api/inventory/product/:productId - 获取单个商品库存（管理员）
- PUT /api/inventory/product/:productId - 更新库存（管理员）
- PUT /api/inventory/batch - 批量更新库存（管理员）
- GET /api/inventory/low-stock - 获取低库存商品（管理员）
- GET /api/promotions - 获取促销活动列表
- GET /api/promotions/:id - 获取单个促销活动
- GET /api/promotions/:id/check - 检查促销活动有效性
- POST /api/promotions - 创建促销活动（管理员）
- PUT /api/promotions/:id - 更新促销活动（管理员）
- DELETE /api/promotions/:id - 删除促销活动（管理员）
- GET /api/orders - 获取订单列表（认证用户）
- GET /api/orders/:id - 获取单个订单（认证用户）
- POST /api/orders - 创建订单（认证用户）
- PUT /api/orders/:id/status - 更新订单状态（认证用户）
- GET /api/orders/stats/summary - 获取订单统计（管理员）
- GET /api/weather - 获取天气信息
- GET /api/weather/city - 根据IP获取城市信息

## 测试账号
- 管理员：admin / admin123456
- 测试用户：testuser / user123456

## 下一步行动
1. 开始前端界面开发
2. 实现后台管理界面
3. 实现前台用户界面
4. 前后端联调测试
