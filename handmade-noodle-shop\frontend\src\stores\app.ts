import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 加载状态
  const globalLoading = ref(false)
  
  // 当前页面标题
  const pageTitle = ref('手工面食店管理系统')
  
  // 面包屑导航
  const breadcrumbs = ref<Array<{ title: string; path?: string }>>([])
  
  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  // 设置全局加载状态
  const setGlobalLoading = (loading: boolean) => {
    globalLoading.value = loading
  }
  
  // 设置页面标题
  const setPageTitle = (title: string) => {
    pageTitle.value = title
    document.title = title
  }
  
  // 设置面包屑导航
  const setBreadcrumbs = (crumbs: Array<{ title: string; path?: string }>) => {
    breadcrumbs.value = crumbs
  }
  
  return {
    // 状态
    sidebarCollapsed,
    globalLoading,
    pageTitle,
    breadcrumbs,
    
    // 方法
    toggleSidebar,
    setGlobalLoading,
    setPageTitle,
    setBreadcrumbs
  }
})
