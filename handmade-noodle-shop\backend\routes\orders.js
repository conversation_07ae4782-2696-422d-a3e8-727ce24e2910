const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// 获取订单列表（需要认证，管理员可以看所有订单，用户只能看自己的）
router.get('/', authenticateToken, orderController.getOrders);

// 获取单个订单（需要认证，管理员可以看所有订单，用户只能看自己的）
router.get('/:id', authenticateToken, orderController.getOrderById);

// 创建订单（需要认证）
router.post('/', authenticateToken, orderController.createOrder);

// 更新订单状态（需要认证，管理员可以更新所有状态，用户只能取消自己的订单）
router.put('/:id/status', authenticateToken, orderController.updateOrderStatus);

// 获取订单统计（管理员）
router.get('/stats/summary', authenticateToken, requireAdmin, orderController.getOrderStats);

module.exports = router;
