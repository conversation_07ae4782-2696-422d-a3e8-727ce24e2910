import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Product } from '@/api/types'

// 购物车商品项接口
export interface CartItem {
  id: string
  product: Product
  quantity: number // 购买数量（斤）
  unit_price: number // 单价
  subtotal: number // 小计
}

export const useCartStore = defineStore('cart', () => {
  // 状态
  const items = ref<CartItem[]>([])
  
  // 计算属性
  const itemCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })
  
  const totalAmount = computed(() => {
    return items.value.reduce((total, item) => total + item.subtotal, 0)
  })
  
  const isEmpty = computed(() => {
    return items.value.length === 0
  })
  
  // 添加商品到购物车
  const addItem = (product: Product, quantity: number) => {
    const existingItem = items.value.find(item => item.product.id === product.id)
    
    if (existingItem) {
      // 如果商品已存在，更新数量
      existingItem.quantity += quantity
      existingItem.subtotal = existingItem.quantity * existingItem.unit_price
    } else {
      // 添加新商品
      const newItem: CartItem = {
        id: `${product.id}_${Date.now()}`,
        product,
        quantity,
        unit_price: product.price_per_jin,
        subtotal: quantity * product.price_per_jin
      }
      items.value.push(newItem)
    }
    
    // 保存到本地存储
    saveToStorage()
  }
  
  // 更新商品数量
  const updateQuantity = (itemId: string, quantity: number) => {
    const item = items.value.find(item => item.id === itemId)
    if (item) {
      if (quantity <= 0) {
        removeItem(itemId)
      } else {
        item.quantity = quantity
        item.subtotal = quantity * item.unit_price
        saveToStorage()
      }
    }
  }
  
  // 移除商品
  const removeItem = (itemId: string) => {
    const index = items.value.findIndex(item => item.id === itemId)
    if (index > -1) {
      items.value.splice(index, 1)
      saveToStorage()
    }
  }
  
  // 清空购物车
  const clearCart = () => {
    items.value = []
    saveToStorage()
  }
  
  // 检查商品是否在购物车中
  const hasProduct = (productId: number) => {
    return items.value.some(item => item.product.id === productId)
  }
  
  // 获取商品在购物车中的数量
  const getProductQuantity = (productId: number) => {
    const item = items.value.find(item => item.product.id === productId)
    return item ? item.quantity : 0
  }
  
  // 保存到本地存储
  const saveToStorage = () => {
    localStorage.setItem('cart_items', JSON.stringify(items.value))
  }
  
  // 从本地存储加载
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('cart_items')
      if (stored) {
        items.value = JSON.parse(stored)
      }
    } catch (error) {
      console.error('加载购物车数据失败:', error)
      items.value = []
    }
  }
  
  // 初始化购物车
  const initCart = () => {
    loadFromStorage()
  }
  
  return {
    // 状态
    items,
    
    // 计算属性
    itemCount,
    totalAmount,
    isEmpty,
    
    // 方法
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    hasProduct,
    getProductQuantity,
    initCart
  }
}, {
  persist: {
    key: 'cart-store',
    storage: localStorage,
    paths: ['items']
  }
})
