const { Inventory, Product, Category } = require('../models');
const { Op } = require('sequelize');
const response = require('../utils/response');

// 获取库存列表
const getInventory = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      category_id,
      low_stock = false,
      search
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    const whereClause = {};
    const productWhereClause = { status: 'active' };
    
    if (category_id) {
      productWhereClause.category_id = category_id;
    }
    
    if (search) {
      productWhereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { sku: { [Op.like]: `%${search}%` } }
      ];
    }

    // 低库存筛选
    if (low_stock === 'true') {
      whereClause[Op.where] = {
        [Op.col]: 'stock_jin',
        [Op.lte]: { [Op.col]: 'min_stock_jin' }
      };
    }

    // 查询库存
    const { count, rows: inventory } = await Inventory.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          as: 'product',
          where: productWhereClause,
          attributes: ['id', 'sku', 'name', 'price_per_jin', 'status'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order: [['last_updated', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    response.paginated(res, inventory, {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count
    }, '获取库存列表成功');

  } catch (error) {
    console.error('获取库存列表错误:', error);
    response.serverError(res, '获取库存列表失败');
  }
};

// 获取单个商品库存
const getInventoryByProductId = async (req, res) => {
  try {
    const { productId } = req.params;

    const inventory = await Inventory.findOne({
      where: { product_id: productId },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'sku', 'name', 'price_per_jin'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    if (!inventory) {
      return response.notFound(res, '库存记录不存在');
    }

    response.success(res, inventory, '获取库存成功');
  } catch (error) {
    console.error('获取库存错误:', error);
    response.serverError(res, '获取库存失败');
  }
};

// 更新库存（管理员）
const updateInventory = async (req, res) => {
  try {
    const { productId } = req.params;
    const { stock_jin, min_stock_jin, operation_type, change_amount, notes } = req.body;

    const inventory = await Inventory.findOne({
      where: { product_id: productId },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'sku', 'name']
        }
      ]
    });

    if (!inventory) {
      return response.notFound(res, '库存记录不存在');
    }

    // 数据验证
    const errors = [];
    
    if (stock_jin !== undefined && stock_jin < 0) {
      errors.push('库存数量不能为负数');
    }
    
    if (min_stock_jin !== undefined && min_stock_jin < 0) {
      errors.push('最低库存不能为负数');
    }

    if (operation_type && !['set', 'add', 'subtract'].includes(operation_type)) {
      errors.push('操作类型无效');
    }

    if (operation_type && operation_type !== 'set' && !change_amount) {
      errors.push('变更数量不能为空');
    }

    if (errors.length > 0) {
      return response.validationError(res, errors);
    }

    let newStockJin = inventory.stock_jin;

    // 根据操作类型更新库存
    if (operation_type) {
      switch (operation_type) {
        case 'set':
          newStockJin = parseFloat(stock_jin);
          break;
        case 'add':
          newStockJin = parseFloat(inventory.stock_jin) + parseFloat(change_amount);
          break;
        case 'subtract':
          newStockJin = parseFloat(inventory.stock_jin) - parseFloat(change_amount);
          if (newStockJin < 0) {
            return response.error(res, '库存不足，无法减少', 400);
          }
          break;
      }
    } else if (stock_jin !== undefined) {
      newStockJin = parseFloat(stock_jin);
    }

    // 更新库存
    const updateData = {
      stock_jin: newStockJin,
      last_updated: new Date()
    };

    if (min_stock_jin !== undefined) {
      updateData.min_stock_jin = parseFloat(min_stock_jin);
    }

    await inventory.update(updateData);

    // 记录库存变更日志（可选功能）
    // TODO: 实现库存变更日志记录

    // 获取更新后的库存信息
    const updatedInventory = await Inventory.findOne({
      where: { product_id: productId },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'sku', 'name', 'price_per_jin'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    response.success(res, updatedInventory, '更新库存成功');
  } catch (error) {
    console.error('更新库存错误:', error);
    response.serverError(res, '更新库存失败');
  }
};

// 批量更新库存（管理员）
const batchUpdateInventory = async (req, res) => {
  try {
    const { updates } = req.body; // [{ product_id, stock_jin, min_stock_jin, operation_type, change_amount }]

    if (!Array.isArray(updates) || updates.length === 0) {
      return response.validationError(res, ['更新数据不能为空']);
    }

    const results = [];
    const errors = [];

    for (let i = 0; i < updates.length; i++) {
      const update = updates[i];
      const { product_id, stock_jin, min_stock_jin, operation_type, change_amount } = update;

      try {
        const inventory = await Inventory.findOne({
          where: { product_id },
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'sku', 'name']
            }
          ]
        });

        if (!inventory) {
          errors.push(`商品ID ${product_id} 的库存记录不存在`);
          continue;
        }

        let newStockJin = inventory.stock_jin;

        // 根据操作类型更新库存
        if (operation_type) {
          switch (operation_type) {
            case 'set':
              newStockJin = parseFloat(stock_jin);
              break;
            case 'add':
              newStockJin = parseFloat(inventory.stock_jin) + parseFloat(change_amount);
              break;
            case 'subtract':
              newStockJin = parseFloat(inventory.stock_jin) - parseFloat(change_amount);
              if (newStockJin < 0) {
                errors.push(`商品 ${inventory.product.name} 库存不足，无法减少`);
                continue;
              }
              break;
          }
        } else if (stock_jin !== undefined) {
          newStockJin = parseFloat(stock_jin);
        }

        // 更新库存
        const updateData = {
          stock_jin: newStockJin,
          last_updated: new Date()
        };

        if (min_stock_jin !== undefined) {
          updateData.min_stock_jin = parseFloat(min_stock_jin);
        }

        await inventory.update(updateData);
        results.push({
          product_id,
          product_name: inventory.product.name,
          old_stock: inventory.stock_jin,
          new_stock: newStockJin,
          success: true
        });

      } catch (error) {
        errors.push(`商品ID ${product_id} 更新失败: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return response.error(res, '部分库存更新失败', 400, {
        successful_updates: results,
        errors
      });
    }

    response.success(res, results, '批量更新库存成功');
  } catch (error) {
    console.error('批量更新库存错误:', error);
    response.serverError(res, '批量更新库存失败');
  }
};

// 获取低库存商品
const getLowStockProducts = async (req, res) => {
  try {
    const lowStockInventory = await Inventory.findAll({
      where: {
        [Op.where]: {
          [Op.col]: 'stock_jin',
          [Op.lte]: { [Op.col]: 'min_stock_jin' }
        }
      },
      include: [
        {
          model: Product,
          as: 'product',
          where: { status: 'active' },
          attributes: ['id', 'sku', 'name', 'price_per_jin'],
          include: [
            {
              model: Category,
              as: 'category',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order: [['stock_jin', 'ASC']]
    });

    response.success(res, lowStockInventory, '获取低库存商品成功');
  } catch (error) {
    console.error('获取低库存商品错误:', error);
    response.serverError(res, '获取低库存商品失败');
  }
};

module.exports = {
  getInventory,
  getInventoryByProductId,
  updateInventory,
  batchUpdateInventory,
  getLowStockProducts
};
