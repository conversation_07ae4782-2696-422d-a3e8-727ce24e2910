const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 测试API接口
const testAPI = async () => {
  try {
    console.log('开始测试API接口...\n');

    // 1. 测试管理员登录
    console.log('1. 测试管理员登录');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'admin123456'
    });
    
    console.log('登录成功:', loginResponse.data.message);
    const adminToken = loginResponse.data.data.token;
    console.log('管理员Token获取成功\n');

    // 2. 测试获取分类列表
    console.log('2. 测试获取分类列表');
    const categoriesResponse = await axios.get(`${BASE_URL}/categories`);
    console.log('分类数量:', categoriesResponse.data.data.length);
    console.log('分类列表:', categoriesResponse.data.data.map(c => c.name).join(', '));
    console.log('');

    // 3. 测试获取商品列表
    console.log('3. 测试获取商品列表');
    const productsResponse = await axios.get(`${BASE_URL}/products`);
    console.log('商品数量:', productsResponse.data.data.length);
    console.log('商品列表:', productsResponse.data.data.map(p => p.name).join(', '));
    console.log('');

    // 4. 测试获取库存列表（需要管理员权限）
    console.log('4. 测试获取库存列表');
    const inventoryResponse = await axios.get(`${BASE_URL}/inventory`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    console.log('库存记录数量:', inventoryResponse.data.data.length);
    console.log('');

    // 5. 测试创建新分类（管理员权限）
    console.log('5. 测试创建新分类');
    const randomName = `测试分类${Date.now()}`;
    const newCategoryResponse = await axios.post(`${BASE_URL}/categories`, {
      name: randomName,
      description: '这是一个测试分类',
      sort_order: 10
    }, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    console.log('新分类创建成功:', newCategoryResponse.data.data.name);
    console.log('');

    // 6. 测试用户注册
    console.log('6. 测试用户注册');
    const randomUser = `newuser${Date.now()}`;
    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
      username: randomUser,
      email: `${randomUser}@example.com`,
      password: 'newuser123456',
      phone: `139${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`
    });
    console.log('用户注册成功:', registerResponse.data.data.user.username);
    console.log('');

    // 7. 测试新用户登录
    console.log('7. 测试新用户登录');
    const userLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      username: randomUser,
      password: 'newuser123456'
    });
    console.log('新用户登录成功:', userLoginResponse.data.data.user.username);
    const userToken = userLoginResponse.data.data.token;
    console.log('');

    // 8. 测试获取当前用户信息
    console.log('8. 测试获取当前用户信息');
    const currentUserResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    console.log('当前用户:', currentUserResponse.data.data.username);
    console.log('用户角色:', currentUserResponse.data.data.role);
    console.log('');

    // 9. 测试获取促销活动列表
    console.log('9. 测试获取促销活动列表');
    const promotionsResponse = await axios.get(`${BASE_URL}/promotions`);
    console.log('促销活动数量:', promotionsResponse.data.data.length);
    console.log('促销活动:', promotionsResponse.data.data.map(p => p.title).join(', '));
    console.log('');

    // 10. 测试创建订单
    console.log('10. 测试创建订单');
    const products = await axios.get(`${BASE_URL}/products`);
    const firstProduct = products.data.data[0];

    const orderResponse = await axios.post(`${BASE_URL}/orders`, {
      items: [
        {
          product_id: firstProduct.id,
          quantity_jin: 2.5
        }
      ],
      notes: '测试订单'
    }, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    console.log('订单创建成功:', orderResponse.data.data.order_no);
    const orderId = orderResponse.data.data.id;
    console.log('');

    // 11. 测试获取订单列表
    console.log('11. 测试获取订单列表');
    const ordersResponse = await axios.get(`${BASE_URL}/orders`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });
    console.log('用户订单数量:', ordersResponse.data.data.length);
    console.log('');

    // 12. 测试管理员获取所有订单
    console.log('12. 测试管理员获取所有订单');
    const allOrdersResponse = await axios.get(`${BASE_URL}/orders`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    console.log('所有订单数量:', allOrdersResponse.data.data.length);
    console.log('');

    // 13. 测试天气API
    console.log('13. 测试天气API');
    try {
      const weatherResponse = await axios.get(`${BASE_URL}/weather?city=Beijing`);
      console.log('天气信息获取成功:', weatherResponse.data.data.city);
    } catch (weatherError) {
      console.log('天气API未配置或不可用（这是正常的）');
    }
    console.log('');

    console.log('✅ 所有API测试通过！');

  } catch (error) {
    console.error('❌ API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
};

// 运行测试
testAPI();
